{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\SmartCaviFilter.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Box, TextField, InputAdornment, IconButton, FormControl, InputLabel, Select, MenuItem, Typography, Chip, Button } from '@mui/material';\nimport { Search as SearchIcon, Clear as ClearIcon, Cancel as CancelIcon, CheckBox as CheckBoxIcon, CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon } from '@mui/icons-material';\n\n/**\n * Componente per il filtraggio intelligente dei cavi\n * Implementa un sistema di ricerca simile a quello delle bobine con supporto per termini multipli separati da virgola\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista completa dei cavi\n * @param {Function} props.onFilteredDataChange - Callback chiamata quando i dati filtrati cambiano\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmartCaviFilter = ({\n  cavi = [],\n  onFilteredDataChange = null,\n  loading = false,\n  selectionEnabled = false,\n  onSelectionToggle = null\n}) => {\n  _s();\n  const [searchText, setSearchText] = useState('');\n  const [searchType, setSearchType] = useState('contains'); // 'contains' o 'equals'\n\n  /**\n   * Normalizza una stringa per la ricerca (lowercase, trim)\n   */\n  const normalizeString = str => {\n    return String(str || '').toLowerCase().trim();\n  };\n\n  /**\n   * Mappa di sinonimi e alias per ricerca intelligente\n   */\n  const synonymMap = {\n    // Bobina vuota\n    'vuota': 'bobina_vuota',\n    'empty': 'bobina_vuota',\n    'senza bobina': 'bobina_vuota',\n    'no bobina': 'bobina_vuota',\n    // Utility\n    'mt': ['mt', 'media tensione'],\n    'bt': ['bt', 'bassa tensione'],\n    'tlc': ['tlc', 'telecomunicazioni'],\n    // Stati\n    'installato': 'installato',\n    'posato': 'installato',\n    'da installare': 'da_installare',\n    'da posare': 'da_installare',\n    'in corso': 'in_corso',\n    // Sistemi\n    'principale': 'principale',\n    'main': 'principale',\n    'ausiliario': 'ausiliario',\n    'aux': 'ausiliario',\n    'emergency': 'emergency',\n    'emergenza': 'emergency'\n  };\n\n  /**\n   * Espande un termine di ricerca con sinonimi\n   */\n  const expandSearchTerm = term => {\n    const normalizedTerm = normalizeString(term);\n    const synonyms = synonymMap[normalizedTerm];\n    if (synonyms) {\n      if (Array.isArray(synonyms)) {\n        return synonyms;\n      } else {\n        return [normalizedTerm, synonyms];\n      }\n    }\n    return [normalizedTerm];\n  };\n\n  /**\n   * Calcola la distanza di Levenshtein per ricerca fuzzy\n   */\n  const levenshteinDistance = (str1, str2) => {\n    const matrix = [];\n    for (let i = 0; i <= str2.length; i++) {\n      matrix[i] = [i];\n    }\n    for (let j = 0; j <= str1.length; j++) {\n      matrix[0][j] = j;\n    }\n    for (let i = 1; i <= str2.length; i++) {\n      for (let j = 1; j <= str1.length; j++) {\n        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(matrix[i - 1][j - 1] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j] + 1);\n        }\n      }\n    }\n    return matrix[str2.length][str1.length];\n  };\n\n  /**\n   * Verifica se due stringhe sono simili (ricerca fuzzy)\n   */\n  const isFuzzyMatch = (term, target, threshold = 2) => {\n    if (term.length < 3) return false; // Non applicare fuzzy per termini troppo corti\n\n    const distance = levenshteinDistance(term, target);\n    const maxLength = Math.max(term.length, target.length);\n\n    // Permetti errori in base alla lunghezza del termine\n    const allowedErrors = Math.min(threshold, Math.floor(maxLength * 0.3));\n    return distance <= allowedErrors;\n  };\n\n  /**\n   * Verifica se un termine corrisponde a un pattern con wildcards\n   */\n  const matchesWildcard = (pattern, text) => {\n    // Converte pattern con * e ? in regex\n    const regexPattern = pattern.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') // Escape caratteri speciali\n    .replace(/\\\\\\*/g, '.*') // * diventa .*\n    .replace(/\\\\\\?/g, '.'); // ? diventa .\n\n    const regex = new RegExp(`^${regexPattern}$`, 'i');\n    return regex.test(text);\n  };\n\n  /**\n   * Verifica se un termine è un range numerico (es: \"240-400\", \">100\", \"<50\")\n   */\n  const parseNumericRange = term => {\n    // Range: \"240-400\"\n    const rangeMatch = term.match(/^(\\d+(?:\\.\\d+)?)-(\\d+(?:\\.\\d+)?)$/);\n    if (rangeMatch) {\n      return {\n        type: 'range',\n        min: parseFloat(rangeMatch[1]),\n        max: parseFloat(rangeMatch[2])\n      };\n    }\n\n    // Maggiore di: \">100\"\n    const gtMatch = term.match(/^>(\\d+(?:\\.\\d+)?)$/);\n    if (gtMatch) {\n      return {\n        type: 'greater',\n        value: parseFloat(gtMatch[1])\n      };\n    }\n\n    // Minore di: \"<50\"\n    const ltMatch = term.match(/^<(\\d+(?:\\.\\d+)?)$/);\n    if (ltMatch) {\n      return {\n        type: 'less',\n        value: parseFloat(ltMatch[1])\n      };\n    }\n    return null;\n  };\n\n  /**\n   * Verifica se un termine numerico corrisponde a un valore\n   */\n  const matchesNumericTerm = (term, value) => {\n    const numericTerm = parseFloat(term);\n    const numericValue = parseFloat(String(value || '0'));\n    if (isNaN(numericTerm) || isNaN(numericValue)) {\n      return false;\n    }\n    return numericValue === numericTerm;\n  };\n\n  /**\n   * Estrae informazioni dall'ID del cavo per facilitare la ricerca\n   */\n  const getCavoInfo = idCavo => {\n    if (!idCavo) return {\n      number: '',\n      suffix: '',\n      full: ''\n    };\n\n    // Estrae il numero finale (es. \"CANT_001_C001\" -> \"001\")\n    const numberMatch = idCavo.match(/_C(\\d+)$/);\n    const number = numberMatch ? numberMatch[1] : '';\n\n    // Estrae la parte finale completa (es. \"CANT_001_C001\" -> \"C001\")\n    const suffixMatch = idCavo.match(/(C\\d+)$/);\n    const suffix = suffixMatch ? suffixMatch[1] : '';\n    return {\n      number: number,\n      suffix: suffix,\n      full: idCavo\n    };\n  };\n\n  /**\n   * Verifica se un cavo corrisponde a un termine di ricerca (versione intelligente)\n   */\n  const cavoMatchesTerm = (cavo, term, isExactMatch = false) => {\n    const termStr = normalizeString(term);\n    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n\n    // Verifica se è un pattern con wildcards\n    const hasWildcards = termStr.includes('*') || termStr.includes('?');\n\n    // Verifica se è un range numerico\n    const numericRange = parseNumericRange(termStr);\n\n    // Espandi il termine con sinonimi\n    const expandedTerms = expandSearchTerm(termStr);\n\n    // Campi da cercare\n    const cavoInfo = getCavoInfo(cavo.id_cavo);\n    const cavoId = normalizeString(cavoInfo.full);\n    const cavoNumber = normalizeString(cavoInfo.number);\n    const cavoSuffix = normalizeString(cavoInfo.suffix);\n    const tipologia = normalizeString(cavo.tipologia);\n    const sezione = normalizeString(cavo.sezione);\n    const utility = normalizeString(cavo.utility);\n    const sistema = normalizeString(cavo.sistema);\n    const ubicazionePartenza = normalizeString(cavo.ubicazione_partenza);\n    const ubicazioneArrivo = normalizeString(cavo.ubicazione_arrivo);\n    const utenzaPartenza = normalizeString(cavo.utenza_partenza);\n    const utenzaArrivo = normalizeString(cavo.utenza_arrivo);\n\n    // Aggiungi anche il campo bobina per la ricerca\n    const bobina = normalizeString(cavo.id_bobina);\n    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' : cavo.id_bobina === null ? '' : normalizeString(cavo.id_bobina);\n\n    // Array di tutti i campi di testo da cercare\n    const textFields = [cavoId, cavoNumber, cavoSuffix, tipologia, sezione, utility, sistema, ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo, bobina, bobinaDisplay];\n\n    // Array di campi numerici per range\n    const numericFields = [{\n      value: cavo.metri_teorici,\n      name: 'metri_teorici'\n    }, {\n      value: cavo.metratura_reale,\n      name: 'metratura_reale'\n    }, {\n      value: parseFloat(cavo.sezione),\n      name: 'sezione'\n    }];\n\n    // Gestione range numerici\n    if (numericRange) {\n      return numericFields.some(field => {\n        const value = parseFloat(field.value);\n        if (isNaN(value)) return false;\n        switch (numericRange.type) {\n          case 'range':\n            return value >= numericRange.min && value <= numericRange.max;\n          case 'greater':\n            return value > numericRange.value;\n          case 'less':\n            return value < numericRange.value;\n          default:\n            return false;\n        }\n      });\n    }\n\n    // Gestione pattern con wildcards\n    if (hasWildcards) {\n      return textFields.some(field => matchesWildcard(termStr, field));\n    }\n\n    // Ricerca con sinonimi espansi\n    const matchesAnyTerm = fieldValue => {\n      return expandedTerms.some(expandedTerm => {\n        if (isExactMatch) {\n          return fieldValue === expandedTerm;\n        } else {\n          // Ricerca normale\n          if (fieldValue.includes(expandedTerm)) {\n            return true;\n          }\n\n          // Ricerca fuzzy per termini più lunghi\n          if (expandedTerm.length >= 4) {\n            return isFuzzyMatch(expandedTerm, fieldValue);\n          }\n          return false;\n        }\n      });\n    };\n\n    // Applica la ricerca intelligente su tutti i campi\n    return textFields.some(field => matchesAnyTerm(field));\n  };\n\n  /**\n   * Applica il filtro ai cavi\n   */\n  const applyFilter = useCallback(() => {\n    if (!searchText.trim()) {\n      // Se non c'è testo di ricerca, mostra tutti i cavi\n      if (onFilteredDataChange) {\n        onFilteredDataChange(cavi);\n      }\n      return;\n    }\n\n    // Dividi il testo di ricerca in termini separati da virgola\n    const searchTerms = searchText.split(',').map(term => term.trim()).filter(term => term.length > 0);\n    console.log('SmartCaviFilter - Ricerca:', {\n      searchText,\n      searchType,\n      searchTerms,\n      totalCavi: cavi.length\n    });\n    let filtered = [];\n    if (searchType === 'equals') {\n      // Per la ricerca esatta con termini multipli, tutti i termini devono corrispondere (AND)\n      if (searchTerms.length === 1) {\n        // Singolo termine: ricerca esatta\n        filtered = cavi.filter(cavo => {\n          const matches = cavoMatchesTerm(cavo, searchTerms[0], true);\n          if (matches) {\n            console.log('SmartCaviFilter - Match trovato:', {\n              cavo: cavo.id_cavo,\n              term: searchTerms[0],\n              cavoInfo: getCavoInfo(cavo.id_cavo)\n            });\n          }\n          return matches;\n        });\n      } else {\n        // Termini multipli: tutti devono corrispondere\n        filtered = cavi.filter(cavo => searchTerms.every(term => cavoMatchesTerm(cavo, term, true)));\n      }\n    } else {\n      // Per la ricerca con 'contains', almeno un termine deve corrispondere (OR)\n      filtered = cavi.filter(cavo => searchTerms.some(term => cavoMatchesTerm(cavo, term, false)));\n    }\n    console.log('SmartCaviFilter - Risultati:', {\n      filteredCount: filtered.length,\n      filteredIds: filtered.map(c => c.id_cavo)\n    });\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filtered);\n    }\n  }, [searchText, searchType, cavi, onFilteredDataChange]);\n\n  /**\n   * Gestisce il cambio del testo di ricerca\n   */\n  const handleSearchTextChange = event => {\n    setSearchText(event.target.value);\n  };\n\n  /**\n   * Pulisce il filtro\n   */\n  const clearFilter = () => {\n    setSearchText('');\n    setSearchType('contains');\n  };\n\n  /**\n   * Conta i termini di ricerca\n   */\n  const getSearchTermsCount = () => {\n    if (!searchText.trim()) return 0;\n    return searchText.split(',').map(term => term.trim()).filter(term => term.length > 0).length;\n  };\n\n  // Applica il filtro quando cambiano i parametri di ricerca o i dati\n  useEffect(() => {\n    applyFilter();\n  }, [applyFilter]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        gap: 2,\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        size: \"small\",\n        label: \"Ricerca intelligente cavi\",\n        variant: \"outlined\",\n        value: searchText,\n        onChange: handleSearchTextChange,\n        placeholder: \"Cerca cavi...\",\n        disabled: loading,\n        sx: {\n          flexGrow: 1,\n          mr: 2\n        } // Occupa tutto lo spazio disponibile con margine destro\n        ,\n        InputProps: {\n          startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"start\",\n            children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this),\n          endAdornment: searchText ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              \"aria-label\": \"clear search\",\n              onClick: clearFilter,\n              edge: \"end\",\n              children: /*#__PURE__*/_jsxDEV(CancelIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this) : null\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        size: \"small\",\n        sx: {\n          minWidth: '140px',\n          mr: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          id: \"search-type-label\",\n          children: \"Tipo ricerca\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          labelId: \"search-type-label\",\n          value: searchType,\n          label: \"Tipo ricerca\",\n          onChange: e => setSearchType(e.target.value),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"contains\",\n            children: \"Contiene\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"equals\",\n            children: \"Uguale a\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this), searchText && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        size: \"small\",\n        startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 24\n        }, this),\n        onClick: clearFilter,\n        disabled: loading,\n        sx: {\n          mr: 2\n        },\n        children: \"Pulisci\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 11\n      }, this), onSelectionToggle && /*#__PURE__*/_jsxDEV(Button, {\n        variant: selectionEnabled ? \"contained\" : \"outlined\",\n        color: \"primary\",\n        size: \"small\",\n        onClick: onSelectionToggle,\n        startIcon: selectionEnabled ? /*#__PURE__*/_jsxDEV(CheckBoxIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 43\n        }, this) : /*#__PURE__*/_jsxDEV(CheckBoxOutlineBlankIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 62\n        }, this),\n        sx: {\n          fontWeight: 'normal',\n          '&:hover': {\n            fontWeight: 'bold'\n          }\n        },\n        children: selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: 2,\n        flexWrap: 'wrap'\n      },\n      children: [searchText && /*#__PURE__*/_jsxDEV(Chip, {\n        size: \"small\",\n        label: `${getSearchTermsCount()} termine${getSearchTermsCount() > 1 ? 'i' : ''} di ricerca`,\n        color: \"primary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 11\n      }, this), searchText && /*#__PURE__*/_jsxDEV(Chip, {\n        size: \"small\",\n        label: searchType === 'contains' ? 'Ricerca per contenuto' : 'Ricerca esatta',\n        color: \"secondary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 420,\n    columnNumber: 5\n  }, this);\n};\n_s(SmartCaviFilter, \"8VMfgaicilYqlvKzOYpeVhbs5wA=\");\n_c = SmartCaviFilter;\nexport default SmartCaviFilter;\nvar _c;\n$RefreshReg$(_c, \"SmartCaviFilter\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "TextField", "InputAdornment", "IconButton", "FormControl", "InputLabel", "Select", "MenuItem", "Typography", "Chip", "<PERSON><PERSON>", "Search", "SearchIcon", "Clear", "ClearIcon", "Cancel", "CancelIcon", "CheckBox", "CheckBoxIcon", "CheckBoxOutlineBlank", "CheckBoxOutlineBlankIcon", "jsxDEV", "_jsxDEV", "SmartCaviFilter", "cavi", "onFilteredDataChange", "loading", "selectionEnabled", "onSelectionToggle", "_s", "searchText", "setSearchText", "searchType", "setSearchType", "normalizeString", "str", "String", "toLowerCase", "trim", "synonymMap", "expandSearchTerm", "term", "normalizedTerm", "synonyms", "Array", "isArray", "levenshteinDistance", "str1", "str2", "matrix", "i", "length", "j", "char<PERSON>t", "Math", "min", "isFuzzyMatch", "target", "threshold", "distance", "max<PERSON><PERSON><PERSON>", "max", "allowedErrors", "floor", "matchesWildcard", "pattern", "text", "regexPattern", "replace", "regex", "RegExp", "test", "parseNumericRange", "rangeMatch", "match", "type", "parseFloat", "gtMatch", "value", "ltMatch", "matchesNumericTerm", "numericTerm", "numericValue", "isNaN", "getCavoInfo", "idCavo", "number", "suffix", "full", "numberMatch", "suffixMatch", "cavoMatchesTerm", "cavo", "isExactMatch", "termStr", "isNumericTerm", "hasWildcards", "includes", "numericRange", "expandedTerms", "cavoInfo", "id_cavo", "cavoId", "cavoNumber", "cavoSuffix", "tipologia", "sezione", "utility", "sistema", "ubicazionePartenza", "ubicazione_partenza", "ubicazioneArrivo", "ubicazione_arrivo", "utenzaPartenza", "utenza_partenza", "utenzaArrivo", "utenza_arrivo", "bobina", "id_bobina", "bob<PERSON><PERSON><PERSON><PERSON>", "textFields", "numericFields", "metri_te<PERSON>ci", "name", "metratura_reale", "some", "field", "matchesAnyTerm", "fieldValue", "expandedTerm", "applyFilter", "searchTerms", "split", "map", "filter", "console", "log", "totalCavi", "filtered", "matches", "every", "filteredCount", "filteredIds", "c", "handleSearchTextChange", "event", "clearFilter", "getSearchTermsCount", "sx", "mb", "children", "display", "alignItems", "justifyContent", "gap", "size", "label", "variant", "onChange", "placeholder", "disabled", "flexGrow", "mr", "InputProps", "startAdornment", "position", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "endAdornment", "onClick", "edge", "min<PERSON><PERSON><PERSON>", "id", "labelId", "e", "startIcon", "color", "fontWeight", "flexWrap", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/SmartCaviFilter.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Box,\n  TextField,\n  InputAdornment,\n  IconButton,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Typography,\n  Chip,\n  Button\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Clear as ClearIcon,\n  Cancel as CancelIcon,\n  CheckBox as CheckBoxIcon,\n  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon\n} from '@mui/icons-material';\n\n/**\n * Componente per il filtraggio intelligente dei cavi\n * Implementa un sistema di ricerca simile a quello delle bobine con supporto per termini multipli separati da virgola\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista completa dei cavi\n * @param {Function} props.onFilteredDataChange - Callback chiamata quando i dati filtrati cambiano\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n */\nconst SmartCaviFilter = ({\n  cavi = [],\n  onFilteredDataChange = null,\n  loading = false,\n  selectionEnabled = false,\n  onSelectionToggle = null\n}) => {\n  const [searchText, setSearchText] = useState('');\n  const [searchType, setSearchType] = useState('contains'); // 'contains' o 'equals'\n\n  /**\n   * Normalizza una stringa per la ricerca (lowercase, trim)\n   */\n  const normalizeString = (str) => {\n    return String(str || '').toLowerCase().trim();\n  };\n\n  /**\n   * Mappa di sinonimi e alias per ricerca intelligente\n   */\n  const synonymMap = {\n    // Bobina vuota\n    'vuota': 'bobina_vuota',\n    'empty': 'bobina_vuota',\n    'senza bobina': 'bobina_vuota',\n    'no bobina': 'bobina_vuota',\n\n    // Utility\n    'mt': ['mt', 'media tensione'],\n    'bt': ['bt', 'bassa tensione'],\n    'tlc': ['tlc', 'telecomunicazioni'],\n\n    // Stati\n    'installato': 'installato',\n    'posato': 'installato',\n    'da installare': 'da_installare',\n    'da posare': 'da_installare',\n    'in corso': 'in_corso',\n\n    // Sistemi\n    'principale': 'principale',\n    'main': 'principale',\n    'ausiliario': 'ausiliario',\n    'aux': 'ausiliario',\n    'emergency': 'emergency',\n    'emergenza': 'emergency'\n  };\n\n  /**\n   * Espande un termine di ricerca con sinonimi\n   */\n  const expandSearchTerm = (term) => {\n    const normalizedTerm = normalizeString(term);\n    const synonyms = synonymMap[normalizedTerm];\n\n    if (synonyms) {\n      if (Array.isArray(synonyms)) {\n        return synonyms;\n      } else {\n        return [normalizedTerm, synonyms];\n      }\n    }\n\n    return [normalizedTerm];\n  };\n\n  /**\n   * Calcola la distanza di Levenshtein per ricerca fuzzy\n   */\n  const levenshteinDistance = (str1, str2) => {\n    const matrix = [];\n\n    for (let i = 0; i <= str2.length; i++) {\n      matrix[i] = [i];\n    }\n\n    for (let j = 0; j <= str1.length; j++) {\n      matrix[0][j] = j;\n    }\n\n    for (let i = 1; i <= str2.length; i++) {\n      for (let j = 1; j <= str1.length; j++) {\n        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(\n            matrix[i - 1][j - 1] + 1,\n            matrix[i][j - 1] + 1,\n            matrix[i - 1][j] + 1\n          );\n        }\n      }\n    }\n\n    return matrix[str2.length][str1.length];\n  };\n\n  /**\n   * Verifica se due stringhe sono simili (ricerca fuzzy)\n   */\n  const isFuzzyMatch = (term, target, threshold = 2) => {\n    if (term.length < 3) return false; // Non applicare fuzzy per termini troppo corti\n\n    const distance = levenshteinDistance(term, target);\n    const maxLength = Math.max(term.length, target.length);\n\n    // Permetti errori in base alla lunghezza del termine\n    const allowedErrors = Math.min(threshold, Math.floor(maxLength * 0.3));\n\n    return distance <= allowedErrors;\n  };\n\n  /**\n   * Verifica se un termine corrisponde a un pattern con wildcards\n   */\n  const matchesWildcard = (pattern, text) => {\n    // Converte pattern con * e ? in regex\n    const regexPattern = pattern\n      .replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') // Escape caratteri speciali\n      .replace(/\\\\\\*/g, '.*') // * diventa .*\n      .replace(/\\\\\\?/g, '.'); // ? diventa .\n\n    const regex = new RegExp(`^${regexPattern}$`, 'i');\n    return regex.test(text);\n  };\n\n  /**\n   * Verifica se un termine è un range numerico (es: \"240-400\", \">100\", \"<50\")\n   */\n  const parseNumericRange = (term) => {\n    // Range: \"240-400\"\n    const rangeMatch = term.match(/^(\\d+(?:\\.\\d+)?)-(\\d+(?:\\.\\d+)?)$/);\n    if (rangeMatch) {\n      return {\n        type: 'range',\n        min: parseFloat(rangeMatch[1]),\n        max: parseFloat(rangeMatch[2])\n      };\n    }\n\n    // Maggiore di: \">100\"\n    const gtMatch = term.match(/^>(\\d+(?:\\.\\d+)?)$/);\n    if (gtMatch) {\n      return {\n        type: 'greater',\n        value: parseFloat(gtMatch[1])\n      };\n    }\n\n    // Minore di: \"<50\"\n    const ltMatch = term.match(/^<(\\d+(?:\\.\\d+)?)$/);\n    if (ltMatch) {\n      return {\n        type: 'less',\n        value: parseFloat(ltMatch[1])\n      };\n    }\n\n    return null;\n  };\n\n  /**\n   * Verifica se un termine numerico corrisponde a un valore\n   */\n  const matchesNumericTerm = (term, value) => {\n    const numericTerm = parseFloat(term);\n    const numericValue = parseFloat(String(value || '0'));\n    \n    if (isNaN(numericTerm) || isNaN(numericValue)) {\n      return false;\n    }\n    \n    return numericValue === numericTerm;\n  };\n\n  /**\n   * Estrae informazioni dall'ID del cavo per facilitare la ricerca\n   */\n  const getCavoInfo = (idCavo) => {\n    if (!idCavo) return { number: '', suffix: '', full: '' };\n\n    // Estrae il numero finale (es. \"CANT_001_C001\" -> \"001\")\n    const numberMatch = idCavo.match(/_C(\\d+)$/);\n    const number = numberMatch ? numberMatch[1] : '';\n\n    // Estrae la parte finale completa (es. \"CANT_001_C001\" -> \"C001\")\n    const suffixMatch = idCavo.match(/(C\\d+)$/);\n    const suffix = suffixMatch ? suffixMatch[1] : '';\n\n    return {\n      number: number,\n      suffix: suffix,\n      full: idCavo\n    };\n  };\n\n  /**\n   * Verifica se un cavo corrisponde a un termine di ricerca (versione intelligente)\n   */\n  const cavoMatchesTerm = (cavo, term, isExactMatch = false) => {\n    const termStr = normalizeString(term);\n    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n\n    // Verifica se è un pattern con wildcards\n    const hasWildcards = termStr.includes('*') || termStr.includes('?');\n\n    // Verifica se è un range numerico\n    const numericRange = parseNumericRange(termStr);\n\n    // Espandi il termine con sinonimi\n    const expandedTerms = expandSearchTerm(termStr);\n\n    // Campi da cercare\n    const cavoInfo = getCavoInfo(cavo.id_cavo);\n    const cavoId = normalizeString(cavoInfo.full);\n    const cavoNumber = normalizeString(cavoInfo.number);\n    const cavoSuffix = normalizeString(cavoInfo.suffix);\n    const tipologia = normalizeString(cavo.tipologia);\n    const sezione = normalizeString(cavo.sezione);\n    const utility = normalizeString(cavo.utility);\n    const sistema = normalizeString(cavo.sistema);\n    const ubicazionePartenza = normalizeString(cavo.ubicazione_partenza);\n    const ubicazioneArrivo = normalizeString(cavo.ubicazione_arrivo);\n    const utenzaPartenza = normalizeString(cavo.utenza_partenza);\n    const utenzaArrivo = normalizeString(cavo.utenza_arrivo);\n\n    // Aggiungi anche il campo bobina per la ricerca\n    const bobina = normalizeString(cavo.id_bobina);\n    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' :\n                         cavo.id_bobina === null ? '' :\n                         normalizeString(cavo.id_bobina);\n\n    // Array di tutti i campi di testo da cercare\n    const textFields = [\n      cavoId, cavoNumber, cavoSuffix, tipologia, sezione, utility, sistema,\n      ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo,\n      bobina, bobinaDisplay\n    ];\n\n    // Array di campi numerici per range\n    const numericFields = [\n      { value: cavo.metri_teorici, name: 'metri_teorici' },\n      { value: cavo.metratura_reale, name: 'metratura_reale' },\n      { value: parseFloat(cavo.sezione), name: 'sezione' }\n    ];\n\n    // Gestione range numerici\n    if (numericRange) {\n      return numericFields.some(field => {\n        const value = parseFloat(field.value);\n        if (isNaN(value)) return false;\n\n        switch (numericRange.type) {\n          case 'range':\n            return value >= numericRange.min && value <= numericRange.max;\n          case 'greater':\n            return value > numericRange.value;\n          case 'less':\n            return value < numericRange.value;\n          default:\n            return false;\n        }\n      });\n    }\n\n    // Gestione pattern con wildcards\n    if (hasWildcards) {\n      return textFields.some(field => matchesWildcard(termStr, field));\n    }\n\n    // Ricerca con sinonimi espansi\n    const matchesAnyTerm = (fieldValue) => {\n      return expandedTerms.some(expandedTerm => {\n        if (isExactMatch) {\n          return fieldValue === expandedTerm;\n        } else {\n          // Ricerca normale\n          if (fieldValue.includes(expandedTerm)) {\n            return true;\n          }\n\n          // Ricerca fuzzy per termini più lunghi\n          if (expandedTerm.length >= 4) {\n            return isFuzzyMatch(expandedTerm, fieldValue);\n          }\n\n          return false;\n        }\n      });\n    };\n\n    // Applica la ricerca intelligente su tutti i campi\n    return textFields.some(field => matchesAnyTerm(field));\n  };\n\n  /**\n   * Applica il filtro ai cavi\n   */\n  const applyFilter = useCallback(() => {\n    if (!searchText.trim()) {\n      // Se non c'è testo di ricerca, mostra tutti i cavi\n      if (onFilteredDataChange) {\n        onFilteredDataChange(cavi);\n      }\n      return;\n    }\n\n    // Dividi il testo di ricerca in termini separati da virgola\n    const searchTerms = searchText.split(',')\n      .map(term => term.trim())\n      .filter(term => term.length > 0);\n\n    console.log('SmartCaviFilter - Ricerca:', {\n      searchText,\n      searchType,\n      searchTerms,\n      totalCavi: cavi.length\n    });\n\n    let filtered = [];\n\n    if (searchType === 'equals') {\n      // Per la ricerca esatta con termini multipli, tutti i termini devono corrispondere (AND)\n      if (searchTerms.length === 1) {\n        // Singolo termine: ricerca esatta\n        filtered = cavi.filter(cavo => {\n          const matches = cavoMatchesTerm(cavo, searchTerms[0], true);\n          if (matches) {\n            console.log('SmartCaviFilter - Match trovato:', {\n              cavo: cavo.id_cavo,\n              term: searchTerms[0],\n              cavoInfo: getCavoInfo(cavo.id_cavo)\n            });\n          }\n          return matches;\n        });\n      } else {\n        // Termini multipli: tutti devono corrispondere\n        filtered = cavi.filter(cavo =>\n          searchTerms.every(term => cavoMatchesTerm(cavo, term, true))\n        );\n      }\n    } else {\n      // Per la ricerca con 'contains', almeno un termine deve corrispondere (OR)\n      filtered = cavi.filter(cavo =>\n        searchTerms.some(term => cavoMatchesTerm(cavo, term, false))\n      );\n    }\n\n    console.log('SmartCaviFilter - Risultati:', {\n      filteredCount: filtered.length,\n      filteredIds: filtered.map(c => c.id_cavo)\n    });\n\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filtered);\n    }\n  }, [searchText, searchType, cavi, onFilteredDataChange]);\n\n  /**\n   * Gestisce il cambio del testo di ricerca\n   */\n  const handleSearchTextChange = (event) => {\n    setSearchText(event.target.value);\n  };\n\n  /**\n   * Pulisce il filtro\n   */\n  const clearFilter = () => {\n    setSearchText('');\n    setSearchType('contains');\n  };\n\n  /**\n   * Conta i termini di ricerca\n   */\n  const getSearchTermsCount = () => {\n    if (!searchText.trim()) return 0;\n    return searchText.split(',').map(term => term.trim()).filter(term => term.length > 0).length;\n  };\n\n  // Applica il filtro quando cambiano i parametri di ricerca o i dati\n  useEffect(() => {\n    applyFilter();\n  }, [applyFilter]);\n\n  return (\n    <Box sx={{ mb: 3 }}>\n      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: 2, mb: 2 }}>\n        {/* Campo di ricerca principale - esteso */}\n        <TextField\n          size=\"small\"\n          label=\"Ricerca intelligente cavi\"\n          variant=\"outlined\"\n          value={searchText}\n          onChange={handleSearchTextChange}\n          placeholder=\"Cerca cavi...\"\n          disabled={loading}\n          sx={{ flexGrow: 1, mr: 2 }} // Occupa tutto lo spazio disponibile con margine destro\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <SearchIcon fontSize=\"small\" />\n              </InputAdornment>\n            ),\n            endAdornment: searchText ? (\n              <InputAdornment position=\"end\">\n                <IconButton\n                  size=\"small\"\n                  aria-label=\"clear search\"\n                  onClick={clearFilter}\n                  edge=\"end\"\n                >\n                  <CancelIcon fontSize=\"small\" />\n                </IconButton>\n              </InputAdornment>\n            ) : null\n          }}\n        />\n\n        {/* Dropdown per il tipo di ricerca */}\n        <FormControl size=\"small\" sx={{ minWidth: '140px', mr: 2 }}>\n          <InputLabel id=\"search-type-label\">Tipo ricerca</InputLabel>\n          <Select\n            labelId=\"search-type-label\"\n            value={searchType}\n            label=\"Tipo ricerca\"\n            onChange={(e) => setSearchType(e.target.value)}\n            disabled={loading}\n          >\n            <MenuItem value=\"contains\">Contiene</MenuItem>\n            <MenuItem value=\"equals\">Uguale a</MenuItem>\n          </Select>\n        </FormControl>\n\n        {/* Pulsante per pulire tutti i filtri */}\n        {searchText && (\n          <Button\n            variant=\"outlined\"\n            size=\"small\"\n            startIcon={<ClearIcon />}\n            onClick={clearFilter}\n            disabled={loading}\n            sx={{ mr: 2 }}\n          >\n            Pulisci\n          </Button>\n        )}\n\n        {/* Sezione destra: pulsante selezione */}\n        {onSelectionToggle && (\n          <Button\n            variant={selectionEnabled ? \"contained\" : \"outlined\"}\n            color=\"primary\"\n            size=\"small\"\n            onClick={onSelectionToggle}\n            startIcon={selectionEnabled ? <CheckBoxIcon /> : <CheckBoxOutlineBlankIcon />}\n            sx={{\n              fontWeight: 'normal',\n              '&:hover': {\n                fontWeight: 'bold'\n              }\n            }}\n          >\n            {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}\n          </Button>\n        )}\n      </Box>\n\n      {/* Informazioni sui risultati */}\n      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>\n        {/* Chip con informazioni sui termini di ricerca */}\n        {searchText && (\n          <Chip\n            size=\"small\"\n            label={`${getSearchTermsCount()} termine${getSearchTermsCount() > 1 ? 'i' : ''} di ricerca`}\n            color=\"primary\"\n            variant=\"outlined\"\n          />\n        )}\n\n        {/* Chip con tipo di ricerca attivo */}\n        {searchText && (\n          <Chip\n            size=\"small\"\n            label={searchType === 'contains' ? 'Ricerca per contenuto' : 'Ricerca esatta'}\n            color=\"secondary\"\n            variant=\"outlined\"\n          />\n        )}\n      </Box>\n\n\n    </Box>\n  );\n};\n\nexport default SmartCaviFilter;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,MAAM,QACD,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,oBAAoB,IAAIC,wBAAwB,QAC3C,qBAAqB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,eAAe,GAAGA,CAAC;EACvBC,IAAI,GAAG,EAAE;EACTC,oBAAoB,GAAG,IAAI;EAC3BC,OAAO,GAAG,KAAK;EACfC,gBAAgB,GAAG,KAAK;EACxBC,iBAAiB,GAAG;AACtB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;;EAE1D;AACF;AACA;EACE,MAAMqC,eAAe,GAAIC,GAAG,IAAK;IAC/B,OAAOC,MAAM,CAACD,GAAG,IAAI,EAAE,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;EAC/C,CAAC;;EAED;AACF;AACA;EACE,MAAMC,UAAU,GAAG;IACjB;IACA,OAAO,EAAE,cAAc;IACvB,OAAO,EAAE,cAAc;IACvB,cAAc,EAAE,cAAc;IAC9B,WAAW,EAAE,cAAc;IAE3B;IACA,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;IAC9B,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;IAC9B,KAAK,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC;IAEnC;IACA,YAAY,EAAE,YAAY;IAC1B,QAAQ,EAAE,YAAY;IACtB,eAAe,EAAE,eAAe;IAChC,WAAW,EAAE,eAAe;IAC5B,UAAU,EAAE,UAAU;IAEtB;IACA,YAAY,EAAE,YAAY;IAC1B,MAAM,EAAE,YAAY;IACpB,YAAY,EAAE,YAAY;IAC1B,KAAK,EAAE,YAAY;IACnB,WAAW,EAAE,WAAW;IACxB,WAAW,EAAE;EACf,CAAC;;EAED;AACF;AACA;EACE,MAAMC,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAMC,cAAc,GAAGR,eAAe,CAACO,IAAI,CAAC;IAC5C,MAAME,QAAQ,GAAGJ,UAAU,CAACG,cAAc,CAAC;IAE3C,IAAIC,QAAQ,EAAE;MACZ,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;QAC3B,OAAOA,QAAQ;MACjB,CAAC,MAAM;QACL,OAAO,CAACD,cAAc,EAAEC,QAAQ,CAAC;MACnC;IACF;IAEA,OAAO,CAACD,cAAc,CAAC;EACzB,CAAC;;EAED;AACF;AACA;EACE,MAAMI,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAC1C,MAAMC,MAAM,GAAG,EAAE;IAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCD,MAAM,CAACC,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC;IACjB;IAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIL,IAAI,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrCH,MAAM,CAAC,CAAC,CAAC,CAACG,CAAC,CAAC,GAAGA,CAAC;IAClB;IAEA,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIL,IAAI,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;QACrC,IAAIJ,IAAI,CAACK,MAAM,CAACH,CAAC,GAAG,CAAC,CAAC,KAAKH,IAAI,CAACM,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,EAAE;UAC7CH,MAAM,CAACC,CAAC,CAAC,CAACE,CAAC,CAAC,GAAGH,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,MAAM;UACLH,MAAM,CAACC,CAAC,CAAC,CAACE,CAAC,CAAC,GAAGE,IAAI,CAACC,GAAG,CACrBN,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EACxBH,MAAM,CAACC,CAAC,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EACpBH,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,CAAC,GAAG,CACrB,CAAC;QACH;MACF;IACF;IAEA,OAAOH,MAAM,CAACD,IAAI,CAACG,MAAM,CAAC,CAACJ,IAAI,CAACI,MAAM,CAAC;EACzC,CAAC;;EAED;AACF;AACA;EACE,MAAMK,YAAY,GAAGA,CAACf,IAAI,EAAEgB,MAAM,EAAEC,SAAS,GAAG,CAAC,KAAK;IACpD,IAAIjB,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;;IAEnC,MAAMQ,QAAQ,GAAGb,mBAAmB,CAACL,IAAI,EAAEgB,MAAM,CAAC;IAClD,MAAMG,SAAS,GAAGN,IAAI,CAACO,GAAG,CAACpB,IAAI,CAACU,MAAM,EAAEM,MAAM,CAACN,MAAM,CAAC;;IAEtD;IACA,MAAMW,aAAa,GAAGR,IAAI,CAACC,GAAG,CAACG,SAAS,EAAEJ,IAAI,CAACS,KAAK,CAACH,SAAS,GAAG,GAAG,CAAC,CAAC;IAEtE,OAAOD,QAAQ,IAAIG,aAAa;EAClC,CAAC;;EAED;AACF;AACA;EACE,MAAME,eAAe,GAAGA,CAACC,OAAO,EAAEC,IAAI,KAAK;IACzC;IACA,MAAMC,YAAY,GAAGF,OAAO,CACzBG,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IAAA,CACvCA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAAA,CACvBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;;IAE1B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIH,YAAY,GAAG,EAAE,GAAG,CAAC;IAClD,OAAOE,KAAK,CAACE,IAAI,CAACL,IAAI,CAAC;EACzB,CAAC;;EAED;AACF;AACA;EACE,MAAMM,iBAAiB,GAAI/B,IAAI,IAAK;IAClC;IACA,MAAMgC,UAAU,GAAGhC,IAAI,CAACiC,KAAK,CAAC,mCAAmC,CAAC;IAClE,IAAID,UAAU,EAAE;MACd,OAAO;QACLE,IAAI,EAAE,OAAO;QACbpB,GAAG,EAAEqB,UAAU,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9BZ,GAAG,EAAEe,UAAU,CAACH,UAAU,CAAC,CAAC,CAAC;MAC/B,CAAC;IACH;;IAEA;IACA,MAAMI,OAAO,GAAGpC,IAAI,CAACiC,KAAK,CAAC,oBAAoB,CAAC;IAChD,IAAIG,OAAO,EAAE;MACX,OAAO;QACLF,IAAI,EAAE,SAAS;QACfG,KAAK,EAAEF,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC;MAC9B,CAAC;IACH;;IAEA;IACA,MAAME,OAAO,GAAGtC,IAAI,CAACiC,KAAK,CAAC,oBAAoB,CAAC;IAChD,IAAIK,OAAO,EAAE;MACX,OAAO;QACLJ,IAAI,EAAE,MAAM;QACZG,KAAK,EAAEF,UAAU,CAACG,OAAO,CAAC,CAAC,CAAC;MAC9B,CAAC;IACH;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;AACF;AACA;EACE,MAAMC,kBAAkB,GAAGA,CAACvC,IAAI,EAAEqC,KAAK,KAAK;IAC1C,MAAMG,WAAW,GAAGL,UAAU,CAACnC,IAAI,CAAC;IACpC,MAAMyC,YAAY,GAAGN,UAAU,CAACxC,MAAM,CAAC0C,KAAK,IAAI,GAAG,CAAC,CAAC;IAErD,IAAIK,KAAK,CAACF,WAAW,CAAC,IAAIE,KAAK,CAACD,YAAY,CAAC,EAAE;MAC7C,OAAO,KAAK;IACd;IAEA,OAAOA,YAAY,KAAKD,WAAW;EACrC,CAAC;;EAED;AACF;AACA;EACE,MAAMG,WAAW,GAAIC,MAAM,IAAK;IAC9B,IAAI,CAACA,MAAM,EAAE,OAAO;MAAEC,MAAM,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC;;IAExD;IACA,MAAMC,WAAW,GAAGJ,MAAM,CAACX,KAAK,CAAC,UAAU,CAAC;IAC5C,MAAMY,MAAM,GAAGG,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE;;IAEhD;IACA,MAAMC,WAAW,GAAGL,MAAM,CAACX,KAAK,CAAC,SAAS,CAAC;IAC3C,MAAMa,MAAM,GAAGG,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE;IAEhD,OAAO;MACLJ,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEH;IACR,CAAC;EACH,CAAC;;EAED;AACF;AACA;EACE,MAAMM,eAAe,GAAGA,CAACC,IAAI,EAAEnD,IAAI,EAAEoD,YAAY,GAAG,KAAK,KAAK;IAC5D,MAAMC,OAAO,GAAG5D,eAAe,CAACO,IAAI,CAAC;IACrC,MAAMsD,aAAa,GAAG,CAACZ,KAAK,CAACW,OAAO,CAAC,IAAI,CAACX,KAAK,CAACP,UAAU,CAACkB,OAAO,CAAC,CAAC;;IAEpE;IACA,MAAME,YAAY,GAAGF,OAAO,CAACG,QAAQ,CAAC,GAAG,CAAC,IAAIH,OAAO,CAACG,QAAQ,CAAC,GAAG,CAAC;;IAEnE;IACA,MAAMC,YAAY,GAAG1B,iBAAiB,CAACsB,OAAO,CAAC;;IAE/C;IACA,MAAMK,aAAa,GAAG3D,gBAAgB,CAACsD,OAAO,CAAC;;IAE/C;IACA,MAAMM,QAAQ,GAAGhB,WAAW,CAACQ,IAAI,CAACS,OAAO,CAAC;IAC1C,MAAMC,MAAM,GAAGpE,eAAe,CAACkE,QAAQ,CAACZ,IAAI,CAAC;IAC7C,MAAMe,UAAU,GAAGrE,eAAe,CAACkE,QAAQ,CAACd,MAAM,CAAC;IACnD,MAAMkB,UAAU,GAAGtE,eAAe,CAACkE,QAAQ,CAACb,MAAM,CAAC;IACnD,MAAMkB,SAAS,GAAGvE,eAAe,CAAC0D,IAAI,CAACa,SAAS,CAAC;IACjD,MAAMC,OAAO,GAAGxE,eAAe,CAAC0D,IAAI,CAACc,OAAO,CAAC;IAC7C,MAAMC,OAAO,GAAGzE,eAAe,CAAC0D,IAAI,CAACe,OAAO,CAAC;IAC7C,MAAMC,OAAO,GAAG1E,eAAe,CAAC0D,IAAI,CAACgB,OAAO,CAAC;IAC7C,MAAMC,kBAAkB,GAAG3E,eAAe,CAAC0D,IAAI,CAACkB,mBAAmB,CAAC;IACpE,MAAMC,gBAAgB,GAAG7E,eAAe,CAAC0D,IAAI,CAACoB,iBAAiB,CAAC;IAChE,MAAMC,cAAc,GAAG/E,eAAe,CAAC0D,IAAI,CAACsB,eAAe,CAAC;IAC5D,MAAMC,YAAY,GAAGjF,eAAe,CAAC0D,IAAI,CAACwB,aAAa,CAAC;;IAExD;IACA,MAAMC,MAAM,GAAGnF,eAAe,CAAC0D,IAAI,CAAC0B,SAAS,CAAC;IAC9C,MAAMC,aAAa,GAAG3B,IAAI,CAAC0B,SAAS,KAAK,cAAc,GAAG,cAAc,GACnD1B,IAAI,CAAC0B,SAAS,KAAK,IAAI,GAAG,EAAE,GAC5BpF,eAAe,CAAC0D,IAAI,CAAC0B,SAAS,CAAC;;IAEpD;IACA,MAAME,UAAU,GAAG,CACjBlB,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EACpEC,kBAAkB,EAAEE,gBAAgB,EAAEE,cAAc,EAAEE,YAAY,EAClEE,MAAM,EAAEE,aAAa,CACtB;;IAED;IACA,MAAME,aAAa,GAAG,CACpB;MAAE3C,KAAK,EAAEc,IAAI,CAAC8B,aAAa;MAAEC,IAAI,EAAE;IAAgB,CAAC,EACpD;MAAE7C,KAAK,EAAEc,IAAI,CAACgC,eAAe;MAAED,IAAI,EAAE;IAAkB,CAAC,EACxD;MAAE7C,KAAK,EAAEF,UAAU,CAACgB,IAAI,CAACc,OAAO,CAAC;MAAEiB,IAAI,EAAE;IAAU,CAAC,CACrD;;IAED;IACA,IAAIzB,YAAY,EAAE;MAChB,OAAOuB,aAAa,CAACI,IAAI,CAACC,KAAK,IAAI;QACjC,MAAMhD,KAAK,GAAGF,UAAU,CAACkD,KAAK,CAAChD,KAAK,CAAC;QACrC,IAAIK,KAAK,CAACL,KAAK,CAAC,EAAE,OAAO,KAAK;QAE9B,QAAQoB,YAAY,CAACvB,IAAI;UACvB,KAAK,OAAO;YACV,OAAOG,KAAK,IAAIoB,YAAY,CAAC3C,GAAG,IAAIuB,KAAK,IAAIoB,YAAY,CAACrC,GAAG;UAC/D,KAAK,SAAS;YACZ,OAAOiB,KAAK,GAAGoB,YAAY,CAACpB,KAAK;UACnC,KAAK,MAAM;YACT,OAAOA,KAAK,GAAGoB,YAAY,CAACpB,KAAK;UACnC;YACE,OAAO,KAAK;QAChB;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIkB,YAAY,EAAE;MAChB,OAAOwB,UAAU,CAACK,IAAI,CAACC,KAAK,IAAI9D,eAAe,CAAC8B,OAAO,EAAEgC,KAAK,CAAC,CAAC;IAClE;;IAEA;IACA,MAAMC,cAAc,GAAIC,UAAU,IAAK;MACrC,OAAO7B,aAAa,CAAC0B,IAAI,CAACI,YAAY,IAAI;QACxC,IAAIpC,YAAY,EAAE;UAChB,OAAOmC,UAAU,KAAKC,YAAY;QACpC,CAAC,MAAM;UACL;UACA,IAAID,UAAU,CAAC/B,QAAQ,CAACgC,YAAY,CAAC,EAAE;YACrC,OAAO,IAAI;UACb;;UAEA;UACA,IAAIA,YAAY,CAAC9E,MAAM,IAAI,CAAC,EAAE;YAC5B,OAAOK,YAAY,CAACyE,YAAY,EAAED,UAAU,CAAC;UAC/C;UAEA,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,OAAOR,UAAU,CAACK,IAAI,CAACC,KAAK,IAAIC,cAAc,CAACD,KAAK,CAAC,CAAC;EACxD,CAAC;;EAED;AACF;AACA;EACE,MAAMI,WAAW,GAAGnI,WAAW,CAAC,MAAM;IACpC,IAAI,CAAC+B,UAAU,CAACQ,IAAI,CAAC,CAAC,EAAE;MACtB;MACA,IAAIb,oBAAoB,EAAE;QACxBA,oBAAoB,CAACD,IAAI,CAAC;MAC5B;MACA;IACF;;IAEA;IACA,MAAM2G,WAAW,GAAGrG,UAAU,CAACsG,KAAK,CAAC,GAAG,CAAC,CACtCC,GAAG,CAAC5F,IAAI,IAAIA,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,CACxBgG,MAAM,CAAC7F,IAAI,IAAIA,IAAI,CAACU,MAAM,GAAG,CAAC,CAAC;IAElCoF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;MACxC1G,UAAU;MACVE,UAAU;MACVmG,WAAW;MACXM,SAAS,EAAEjH,IAAI,CAAC2B;IAClB,CAAC,CAAC;IAEF,IAAIuF,QAAQ,GAAG,EAAE;IAEjB,IAAI1G,UAAU,KAAK,QAAQ,EAAE;MAC3B;MACA,IAAImG,WAAW,CAAChF,MAAM,KAAK,CAAC,EAAE;QAC5B;QACAuF,QAAQ,GAAGlH,IAAI,CAAC8G,MAAM,CAAC1C,IAAI,IAAI;UAC7B,MAAM+C,OAAO,GAAGhD,eAAe,CAACC,IAAI,EAAEuC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;UAC3D,IAAIQ,OAAO,EAAE;YACXJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;cAC9C5C,IAAI,EAAEA,IAAI,CAACS,OAAO;cAClB5D,IAAI,EAAE0F,WAAW,CAAC,CAAC,CAAC;cACpB/B,QAAQ,EAAEhB,WAAW,CAACQ,IAAI,CAACS,OAAO;YACpC,CAAC,CAAC;UACJ;UACA,OAAOsC,OAAO;QAChB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAD,QAAQ,GAAGlH,IAAI,CAAC8G,MAAM,CAAC1C,IAAI,IACzBuC,WAAW,CAACS,KAAK,CAACnG,IAAI,IAAIkD,eAAe,CAACC,IAAI,EAAEnD,IAAI,EAAE,IAAI,CAAC,CAC7D,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACAiG,QAAQ,GAAGlH,IAAI,CAAC8G,MAAM,CAAC1C,IAAI,IACzBuC,WAAW,CAACN,IAAI,CAACpF,IAAI,IAAIkD,eAAe,CAACC,IAAI,EAAEnD,IAAI,EAAE,KAAK,CAAC,CAC7D,CAAC;IACH;IAEA8F,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAC1CK,aAAa,EAAEH,QAAQ,CAACvF,MAAM;MAC9B2F,WAAW,EAAEJ,QAAQ,CAACL,GAAG,CAACU,CAAC,IAAIA,CAAC,CAAC1C,OAAO;IAC1C,CAAC,CAAC;IAEF,IAAI5E,oBAAoB,EAAE;MACxBA,oBAAoB,CAACiH,QAAQ,CAAC;IAChC;EACF,CAAC,EAAE,CAAC5G,UAAU,EAAEE,UAAU,EAAER,IAAI,EAAEC,oBAAoB,CAAC,CAAC;;EAExD;AACF;AACA;EACE,MAAMuH,sBAAsB,GAAIC,KAAK,IAAK;IACxClH,aAAa,CAACkH,KAAK,CAACxF,MAAM,CAACqB,KAAK,CAAC;EACnC,CAAC;;EAED;AACF;AACA;EACE,MAAMoE,WAAW,GAAGA,CAAA,KAAM;IACxBnH,aAAa,CAAC,EAAE,CAAC;IACjBE,aAAa,CAAC,UAAU,CAAC;EAC3B,CAAC;;EAED;AACF;AACA;EACE,MAAMkH,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACrH,UAAU,CAACQ,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;IAChC,OAAOR,UAAU,CAACsG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC5F,IAAI,IAAIA,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,CAACgG,MAAM,CAAC7F,IAAI,IAAIA,IAAI,CAACU,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;EAC9F,CAAC;;EAED;EACArD,SAAS,CAAC,MAAM;IACdoI,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,oBACE5G,OAAA,CAACtB,GAAG;IAACoJ,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACjBhI,OAAA,CAACtB,GAAG;MAACoJ,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE,eAAe;QAAEC,GAAG,EAAE,CAAC;QAAEL,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEjGhI,OAAA,CAACrB,SAAS;QACR0J,IAAI,EAAC,OAAO;QACZC,KAAK,EAAC,2BAA2B;QACjCC,OAAO,EAAC,UAAU;QAClB/E,KAAK,EAAEhD,UAAW;QAClBgI,QAAQ,EAAEd,sBAAuB;QACjCe,WAAW,EAAC,eAAe;QAC3BC,QAAQ,EAAEtI,OAAQ;QAClB0H,EAAE,EAAE;UAAEa,QAAQ,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE,CAAC;QAAA;QAC5BC,UAAU,EAAE;UACVC,cAAc,eACZ9I,OAAA,CAACpB,cAAc;YAACmK,QAAQ,EAAC,OAAO;YAAAf,QAAA,eAC9BhI,OAAA,CAACV,UAAU;cAAC0J,QAAQ,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACjB;UACDC,YAAY,EAAE7I,UAAU,gBACtBR,OAAA,CAACpB,cAAc;YAACmK,QAAQ,EAAC,KAAK;YAAAf,QAAA,eAC5BhI,OAAA,CAACnB,UAAU;cACTwJ,IAAI,EAAC,OAAO;cACZ,cAAW,cAAc;cACzBiB,OAAO,EAAE1B,WAAY;cACrB2B,IAAI,EAAC,KAAK;cAAAvB,QAAA,eAEVhI,OAAA,CAACN,UAAU;gBAACsJ,QAAQ,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,GACf;QACN;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGFpJ,OAAA,CAAClB,WAAW;QAACuJ,IAAI,EAAC,OAAO;QAACP,EAAE,EAAE;UAAE0B,QAAQ,EAAE,OAAO;UAAEZ,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,gBACzDhI,OAAA,CAACjB,UAAU;UAAC0K,EAAE,EAAC,mBAAmB;UAAAzB,QAAA,EAAC;QAAY;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5DpJ,OAAA,CAAChB,MAAM;UACL0K,OAAO,EAAC,mBAAmB;UAC3BlG,KAAK,EAAE9C,UAAW;UAClB4H,KAAK,EAAC,cAAc;UACpBE,QAAQ,EAAGmB,CAAC,IAAKhJ,aAAa,CAACgJ,CAAC,CAACxH,MAAM,CAACqB,KAAK,CAAE;UAC/CkF,QAAQ,EAAEtI,OAAQ;UAAA4H,QAAA,gBAElBhI,OAAA,CAACf,QAAQ;YAACuE,KAAK,EAAC,UAAU;YAAAwE,QAAA,EAAC;UAAQ;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC9CpJ,OAAA,CAACf,QAAQ;YAACuE,KAAK,EAAC,QAAQ;YAAAwE,QAAA,EAAC;UAAQ;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGb5I,UAAU,iBACTR,OAAA,CAACZ,MAAM;QACLmJ,OAAO,EAAC,UAAU;QAClBF,IAAI,EAAC,OAAO;QACZuB,SAAS,eAAE5J,OAAA,CAACR,SAAS;UAAAyJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBE,OAAO,EAAE1B,WAAY;QACrBc,QAAQ,EAAEtI,OAAQ;QAClB0H,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,EACf;MAED;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAGA9I,iBAAiB,iBAChBN,OAAA,CAACZ,MAAM;QACLmJ,OAAO,EAAElI,gBAAgB,GAAG,WAAW,GAAG,UAAW;QACrDwJ,KAAK,EAAC,SAAS;QACfxB,IAAI,EAAC,OAAO;QACZiB,OAAO,EAAEhJ,iBAAkB;QAC3BsJ,SAAS,EAAEvJ,gBAAgB,gBAAGL,OAAA,CAACJ,YAAY;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGpJ,OAAA,CAACF,wBAAwB;UAAAmJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC9EtB,EAAE,EAAE;UACFgC,UAAU,EAAE,QAAQ;UACpB,SAAS,EAAE;YACTA,UAAU,EAAE;UACd;QACF,CAAE;QAAA9B,QAAA,EAED3H,gBAAgB,GAAG,sBAAsB,GAAG;MAAmB;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpJ,OAAA,CAACtB,GAAG;MAACoJ,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEE,GAAG,EAAE,CAAC;QAAE2B,QAAQ,EAAE;MAAO,CAAE;MAAA/B,QAAA,GAE1ExH,UAAU,iBACTR,OAAA,CAACb,IAAI;QACHkJ,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE,GAAGT,mBAAmB,CAAC,CAAC,WAAWA,mBAAmB,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,aAAc;QAC5FgC,KAAK,EAAC,SAAS;QACftB,OAAO,EAAC;MAAU;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACF,EAGA5I,UAAU,iBACTR,OAAA,CAACb,IAAI;QACHkJ,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE5H,UAAU,KAAK,UAAU,GAAG,uBAAuB,GAAG,gBAAiB;QAC9EmJ,KAAK,EAAC,WAAW;QACjBtB,OAAO,EAAC;MAAU;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAAC7I,EAAA,CAhfIN,eAAe;AAAA+J,EAAA,GAAf/J,eAAe;AAkfrB,eAAeA,eAAe;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}