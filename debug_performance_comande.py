#!/usr/bin/env python3
"""
Script per analizzare le performance del caricamento comande
"""

import time
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_db_connection():
    """Connessione al database PostgreSQL"""
    return psycopg2.connect(
        host="localhost",
        database="cantieri",
        user="postgres",
        password="Taranto",
        cursor_factory=RealDictCursor
    )

def measure_query_time(cursor, query, params=None, description="Query"):
    """Misura il tempo di esecuzione di una query"""
    start_time = time.time()
    cursor.execute(query, params)
    results = cursor.fetchall()
    end_time = time.time()
    
    execution_time = (end_time - start_time) * 1000  # in millisecondi
    logging.info(f"⏱️ {description}: {execution_time:.2f}ms - {len(results)} risultati")
    return results, execution_time

def analyze_comande_performance():
    """Analizza le performance delle query delle comande"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            print("🔍 ANALISI PERFORMANCE CARICAMENTO COMANDE")
            print("=" * 60)
            
            # Test 1: Query semplice comande senza JOIN
            print("\n📊 1. QUERY SEMPLICE COMANDE (senza JOIN)")
            query1 = """
                SELECT codice_comanda, tipo_comanda, descrizione, responsabile, stato, 
                       data_creazione, data_scadenza, id_cantiere
                FROM Comande 
                WHERE id_cantiere = %s 
                ORDER BY data_creazione DESC
            """
            results1, time1 = measure_query_time(cursor, query1, (1,), "Query semplice comande")
            
            # Test 2: Query con JOIN responsabili (query attuale)
            print("\n📊 2. QUERY CON JOIN RESPONSABILI (query attuale)")
            query2 = """
                SELECT
                    c.*,
                    r.telefono as responsabile_telefono,
                    r.email as responsabile_email
                FROM Comande c
                LEFT JOIN Responsabili r ON (
                    c.responsabile = r.nome_responsabile
                    AND c.id_cantiere = r.id_cantiere
                    AND r.attivo = TRUE
                )
                WHERE c.id_cantiere = %s
                ORDER BY c.data_creazione DESC
            """
            results2, time2 = measure_query_time(cursor, query2, (1,), "Query con JOIN responsabili")
            
            # Test 3: Verifica indici
            print("\n📊 3. VERIFICA INDICI")
            
            # Indici su tabella Comande
            cursor.execute("""
                SELECT indexname, indexdef 
                FROM pg_indexes 
                WHERE tablename = 'comande'
            """)
            indici_comande = cursor.fetchall()
            print("Indici su tabella Comande:")
            for idx in indici_comande:
                print(f"  - {idx['indexname']}: {idx['indexdef']}")
            
            # Indici su tabella Responsabili
            cursor.execute("""
                SELECT indexname, indexdef 
                FROM pg_indexes 
                WHERE tablename = 'responsabili'
            """)
            indici_responsabili = cursor.fetchall()
            print("\nIndici su tabella Responsabili:")
            for idx in indici_responsabili:
                print(f"  - {idx['indexname']}: {idx['indexdef']}")
            
            # Test 4: Conteggio record
            print("\n📊 4. CONTEGGIO RECORD")
            
            cursor.execute("SELECT COUNT(*) as total FROM Comande WHERE id_cantiere = %s", (1,))
            count_comande = cursor.fetchone()['total']
            print(f"Comande nel cantiere 1: {count_comande}")
            
            cursor.execute("SELECT COUNT(*) as total FROM Responsabili WHERE id_cantiere = %s", (1,))
            count_responsabili = cursor.fetchone()['total']
            print(f"Responsabili nel cantiere 1: {count_responsabili}")
            
            # Test 5: EXPLAIN ANALYZE della query lenta
            print("\n📊 5. EXPLAIN ANALYZE QUERY CON JOIN")
            cursor.execute(f"EXPLAIN ANALYZE {query2}", (1,))
            explain_results = cursor.fetchall()
            print("Piano di esecuzione:")
            for row in explain_results:
                print(f"  {row[0]}")
            
            # Test 6: Query ottimizzata con subquery
            print("\n📊 6. QUERY OTTIMIZZATA CON SUBQUERY")
            query3 = """
                SELECT 
                    c.*,
                    (SELECT r.telefono FROM Responsabili r 
                     WHERE r.nome_responsabile = c.responsabile 
                       AND r.id_cantiere = c.id_cantiere 
                       AND r.attivo = TRUE 
                     LIMIT 1) as responsabile_telefono,
                    (SELECT r.email FROM Responsabili r 
                     WHERE r.nome_responsabile = c.responsabile 
                       AND r.id_cantiere = c.id_cantiere 
                       AND r.attivo = TRUE 
                     LIMIT 1) as responsabile_email
                FROM Comande c
                WHERE c.id_cantiere = %s
                ORDER BY c.data_creazione DESC
            """
            results3, time3 = measure_query_time(cursor, query3, (1,), "Query ottimizzata con subquery")
            
            # Test 7: Query con INNER JOIN (solo comande con responsabili)
            print("\n📊 7. QUERY CON INNER JOIN")
            query4 = """
                SELECT
                    c.*,
                    r.telefono as responsabile_telefono,
                    r.email as responsabile_email
                FROM Comande c
                INNER JOIN Responsabili r ON (
                    c.responsabile = r.nome_responsabile
                    AND c.id_cantiere = r.id_cantiere
                    AND r.attivo = TRUE
                )
                WHERE c.id_cantiere = %s
                ORDER BY c.data_creazione DESC
            """
            results4, time4 = measure_query_time(cursor, query4, (1,), "Query con INNER JOIN")
            
            # Riepilogo performance
            print("\n📈 RIEPILOGO PERFORMANCE")
            print("=" * 60)
            print(f"1. Query semplice (senza JOIN):     {time1:.2f}ms")
            print(f"2. Query attuale (LEFT JOIN):       {time2:.2f}ms")
            print(f"3. Query ottimizzata (subquery):    {time3:.2f}ms")
            print(f"4. Query con INNER JOIN:            {time4:.2f}ms")
            
            # Calcola il rallentamento
            if time1 > 0:
                slowdown = (time2 / time1) * 100
                print(f"\n⚠️ Il JOIN rallenta la query del {slowdown:.1f}%")
            
            # Raccomandazioni
            print("\n💡 RACCOMANDAZIONI")
            print("=" * 60)
            
            if time2 > 100:  # Se la query impiega più di 100ms
                print("🔴 PROBLEMA: Query troppo lenta (>100ms)")
                
                if time1 < 50 and time2 > time1 * 2:
                    print("✅ SOLUZIONE 1: Rimuovere il JOIN e fare query separate")
                    print("✅ SOLUZIONE 2: Aggiungere indici compositi")
                    print("✅ SOLUZIONE 3: Usare cache per i dati dei responsabili")
                
                # Verifica se mancano indici
                indici_necessari = [
                    "idx_comande_cantiere_data",
                    "idx_responsabili_cantiere_nome"
                ]
                
                indici_esistenti = [idx['indexname'] for idx in indici_comande + indici_responsabili]
                indici_mancanti = [idx for idx in indici_necessari if idx not in indici_esistenti]
                
                if indici_mancanti:
                    print(f"⚠️ INDICI MANCANTI: {', '.join(indici_mancanti)}")
                    print("SQL per creare indici:")
                    print("CREATE INDEX idx_comande_cantiere_data ON Comande(id_cantiere, data_creazione DESC);")
                    print("CREATE INDEX idx_responsabili_cantiere_nome ON Responsabili(id_cantiere, nome_responsabile, attivo);")
            
            elif time2 < 50:
                print("🟢 PERFORMANCE OK: Query sufficientemente veloce (<50ms)")
            else:
                print("🟡 PERFORMANCE ACCETTABILE: Query moderatamente lenta (50-100ms)")
                
    except Exception as e:
        logging.error(f"❌ Errore nell'analisi: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_comande_performance()
