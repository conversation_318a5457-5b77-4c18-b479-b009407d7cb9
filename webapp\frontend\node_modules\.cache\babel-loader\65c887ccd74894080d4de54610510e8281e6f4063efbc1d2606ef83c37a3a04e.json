{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Button, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, List, ListItem, ListItemText, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Assignment as AssignIcon, Person as PersonIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, People as PeopleIcon, Construction as ConstructionIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\nimport CollegamentiDialog from './CollegamentiDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n\n  // Stati per dialog collegamenti\n  const [openCollegamenti, setOpenCollegamenti] = useState(false);\n  const [comandaPerCollegamenti, setComandaPerCollegamenti] = useState(null);\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const startTime = performance.now();\n      const comandeData = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n      const endTime = performance.now();\n      console.log(`✅ ${comandeArray.length} comande caricate in ${(endTime - startTime).toFixed(2)}ms`);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n      setAllComande([]);\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const startTime = performance.now();\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      const endTime = performance.now();\n      console.log(`📊 Statistiche caricate in ${(endTime - startTime).toFixed(2)}ms:`, stats);\n      setStatistiche(stats);\n    } catch (err) {\n      var _err$response;\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n      // Imposta statistiche vuote in caso di errore\n      setStatistiche({\n        responsabili_attivi: 0,\n        totale_comande: 0,\n        comande_create: 0,\n        comande_in_corso: 0,\n        comande_completate: 0,\n        comande_annullate: 0\n      });\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n\n        // Per comande di workflow (POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO) provenienti da visualizza cavi,\n        // apri l'inserimento dati specifico. Per altre comande, apri il dialog di modifica generale\n        const isWorkflowCommand = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comandaTrovata.tipo_comanda);\n        if (isWorkflowCommand) {\n          console.log(`📏 Apertura gestione per comanda ${comandaTrovata.tipo_comanda} da visualizza cavi:`, comandaParam);\n          handleOpenInserimentoMetri(comandaTrovata);\n        } else {\n          console.log('✏️ Apertura modifica per comanda non-workflow:', comandaParam);\n          handleOpenComandaDialog(comandaTrovata);\n        }\n\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      console.log('🚀 Caricamento comande per responsabili ottimizzato...');\n      const startTime = performance.now();\n\n      // OTTIMIZZAZIONE: Carica tutte le comande una sola volta invece di fare N chiamate\n      const allComande = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(allComande)) {\n        comandeArray = allComande;\n      } else if (allComande && Array.isArray(allComande.comande)) {\n        comandeArray = allComande.comande;\n      } else if (allComande && Array.isArray(allComande.data)) {\n        comandeArray = allComande.data;\n      }\n\n      // Raggruppa le comande per responsabile\n      const comandeMap = {};\n\n      // Inizializza tutti i responsabili con array vuoto\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n\n      // Raggruppa le comande per responsabile\n      comandeArray.forEach(comanda => {\n        const responsabile = responsabiliList.find(r => r.nome_responsabile === comanda.responsabile);\n        if (responsabile) {\n          comandeMap[responsabile.id_responsabile].push(comanda);\n        }\n      });\n      const endTime = performance.now();\n      console.log(`✅ Comande caricate in ${(endTime - startTime).toFixed(2)}ms (1 chiamata invece di ${responsabiliList.length})`);\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle comande:', err);\n      // Fallback: inizializza con array vuoti\n      const comandeMap = {};\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n      setComandePerResponsabile(comandeMap);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = comanda => {\n    setSelectedComanda(comanda);\n    if (comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n      handleCloseComandaDialog();\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  // Gestione inserimento metri e collegamenti\n  const handleOpenInserimentoMetri = comanda => {\n    if (comanda.tipo_comanda === 'POSA') {\n      // Per POSA: apri dialog inserimento metri\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    } else if (['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda)) {\n      // Per COLLEGAMENTI: apri dialog collegamenti\n      setComandaPerCollegamenti(comanda);\n      setOpenCollegamenti(true);\n    } else {\n      // Per altri tipi: apri dialog inserimento metri (fallback)\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    }\n  };\n  const handleCloseInserimentoMetri = () => {\n    setOpenInserimentoMetri(false);\n    setComandaPerMetri(null);\n  };\n  const handleCloseCollegamenti = () => {\n    setOpenCollegamenti(false);\n    setComandaPerCollegamenti(null);\n  };\n  const handleSuccessInserimentoMetri = async message => {\n    console.log('✅ Successo inserimento metri:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n  const handleSuccessCollegamenti = async message => {\n    console.log('✅ Successo collegamenti:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  // Gestione stampa comanda\n  const handlePrintComanda = comanda => {\n    console.log('🖨️ Stampa comanda:', comanda.codice_comanda);\n\n    // Per ora mostra un dialog di selezione formato\n    const formato = window.prompt(`Seleziona il formato di stampa per la comanda ${comanda.codice_comanda}:\\n\\n` + '1 - A4 (Formato standard)\\n' + '2 - A3 (Formato esteso)\\n\\n' + 'Inserisci 1 o 2:', '1');\n    if (formato === '1' || formato === '2') {\n      const formatoNome = formato === '1' ? 'A4' : 'A3';\n      console.log(`📄 Generazione comanda ${comanda.codice_comanda} in formato ${formatoNome}`);\n\n      // TODO: Implementare la generazione del PDF\n      alert(`Funzionalità in sviluppo!\\n\\nComanda: ${comanda.codice_comanda}\\nFormato: ${formatoNome}\\nTipo: ${comanda.tipo_comanda}\\nResponsabile: ${comanda.responsabile}\\nCavi: ${comanda.numero_cavi_assegnati || 0}`);\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading || loadingComande) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 9\n    }, this), searchingComanda && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"\\uD83D\\uDD0D Ricerca comanda \", searchingComanda, \" in corso...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Gestione Responsabili e Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 28\n              }, this),\n              onClick: () => setOpenResponsabiliPopup(true),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f5f7fa',\n                color: '#2196f3',\n                border: '1px solid #2196f3',\n                '&:hover': {\n                  backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                  borderColor: '#1976d2'\n                }\n              },\n              children: \"Lista Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#2196f3',\n                color: 'white',\n                '&:hover': {\n                  backgroundColor: '#1976d2'\n                }\n              },\n              children: \"Crea Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this), loadingComande ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 13\n        }, this) : allComande.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Crea la prima comanda per iniziare a gestire i lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              backgroundColor: '#2196f3',\n              color: 'white',\n              '&:hover': {\n                backgroundColor: '#1976d2'\n              }\n            },\n            children: \"Crea Prima Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ComandeListTable, {\n          comande: allComande,\n          onEditComanda: handleOpenComandaDialog,\n          onDeleteComanda: handleDeleteComanda,\n          onInserimentoMetri: handleOpenInserimentoMetri,\n          onPrintComanda: handlePrintComanda,\n          loading: loadingComande\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 772,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: [\"Modifica Comanda \", selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.codice_comanda]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Stato: \", selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.stato, \" \\u2022 Cavi assegnati: \", (selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.numero_cavi_assegnati) || 0]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Attenzione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 17\n              }, this), \" Modificare il tipo di comanda riassegner\\xE0 automaticamente tutti i cavi al nuovo tipo. Assicurati che i cavi siano compatibili con il nuovo tipo di operazione.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            select: true,\n            label: \"Tipo Comanda\",\n            value: formDataComanda.tipo_comanda,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              tipo_comanda: e.target.value\n            }),\n            margin: \"normal\",\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"POSA\",\n              children: \"Posa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_PARTENZA\",\n              children: \"Collegamento Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_ARRIVO\",\n              children: \"Collegamento Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"CERTIFICAZIONE\",\n              children: \"Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Descrizione\",\n            value: formDataComanda.descrizione,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              descrizione: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3,\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Responsabile\",\n            value: formDataComanda.responsabile,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Note Capo Cantiere\",\n            value: formDataComanda.note_capo_cantiere,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              note_capo_cantiere: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 2,\n            helperText: \"Istruzioni specifiche per il responsabile\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Data Scadenza\",\n            type: \"date\",\n            value: formDataComanda.data_scadenza,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              data_scadenza: e.target.value\n            }),\n            margin: \"normal\",\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 939,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: (response, successMessage) => {\n        console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n        // Mostra messaggio di successo se fornito\n        if (successMessage) {\n          // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n          console.log('📢 Successo:', successMessage);\n        }\n\n        // Ricarica tutti i dati per aggiornare l'interfaccia\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n        console.log('✅ Interfaccia aggiornata');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 954,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsabiliListPopup, {\n      open: openResponsabiliPopup,\n      onClose: () => setOpenResponsabiliPopup(false),\n      responsabili: responsabili,\n      comandePerResponsabile: comandePerResponsabile,\n      onEditResponsabile: responsabile => {\n        setOpenResponsabiliPopup(false);\n        handleOpenResponsabileDialog('edit', responsabile);\n      },\n      onDeleteResponsabile: async idResponsabile => {\n        await handleDeleteResponsabile(idResponsabile);\n        setOpenResponsabiliPopup(false);\n      },\n      loading: loadingResponsabili,\n      error: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 978,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InserimentoMetriDialog, {\n      open: openInserimentoMetri,\n      onClose: handleCloseInserimentoMetri,\n      comanda: comandaPerMetri,\n      onSuccess: handleSuccessInserimentoMetri\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 996,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CollegamentiDialog, {\n      open: openCollegamenti,\n      onClose: handleCloseCollegamenti,\n      comanda: comandaPerCollegamenti,\n      onSuccess: handleSuccessCollegamenti\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1004,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 564,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"z85HBrPxMvigfS49H7k5EdvaEB0=\", false, function () {\n  return [useSearchParams];\n});\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "People", "PeopleIcon", "Construction", "ConstructionIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "ResponsabiliListPopup", "ComandeListTable", "InserimentoMetriDialog", "CollegamentiDialog", "jsxDEV", "_jsxDEV", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "searchParams", "setSearchParams", "loading", "setLoading", "error", "setError", "searchingComanda", "setSearchingComanda", "statistiche", "setStatistiche", "allComande", "setAllComande", "loadingComande", "setLoadingComande", "openResponsabiliPopup", "setOpenResponsabiliPopup", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "note_capo_cantiere", "openInserimentoMetri", "setOpenInserimentoMetri", "comandaPerMetri", "setComandaPerMetri", "openCollegamenti", "setOpenCollegamenti", "comandaPerCollegamenti", "setComandaPerCollegamenti", "loadComande", "console", "log", "startTime", "performance", "now", "comandeData", "getComande", "comandeArray", "Array", "isArray", "comande", "data", "endTime", "length", "toFixed", "err", "loadStatistiche", "stats", "getStatisticheComande", "_err$response", "response", "message", "responsabili_attivi", "totale_comande", "comande_create", "comande_in_corso", "comande_completate", "comande_annullate", "loadResponsabili", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response2", "_err$response2$data", "errorMessage", "detail", "initializeData", "Promise", "all", "comandaParam", "get", "Object", "keys", "comandaTrovata", "comandeResp", "id_responsabile", "find", "c", "codice_comanda", "isWorkflowCommand", "includes", "handleOpenInserimentoMetri", "handleOpenComandaDialog", "setTimeout", "prev", "newParams", "URLSearchParams", "delete", "warn", "for<PERSON>ach", "resp", "cmd", "responsabiliList", "comandeMap", "comanda", "r", "push", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "handleCloseInserimentoMetri", "handleCloseCollegamenti", "handleSuccessInserimentoMetri", "handleSuccessCollegamenti", "handlePrintComanda", "formato", "prompt", "formatoNome", "alert", "numero_cavi_assegnati", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "width", "height", "borderRadius", "Math", "round", "gap", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "borderColor", "elevation", "textAlign", "gutterBottom", "onEditComanda", "onDeleteComanda", "onInserimentoMetri", "onPrintComanda", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "pt", "label", "value", "onChange", "e", "target", "margin", "required", "type", "helperText", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "successMessage", "onEditResponsabile", "onDeleteResponsabile", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  List,\n  ListItem,\n  ListItemText,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  People as PeopleIcon,\n  Construction as ConstructionIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\nimport CollegamentiDialog from './CollegamentiDialog';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n\n  // Stati per dialog collegamenti\n  const [openCollegamenti, setOpenCollegamenti] = useState(false);\n  const [comandaPerCollegamenti, setComandaPerCollegamenti] = useState(null);\n\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const startTime = performance.now();\n\n      const comandeData = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n\n      const endTime = performance.now();\n      console.log(`✅ ${comandeArray.length} comande caricate in ${(endTime - startTime).toFixed(2)}ms`);\n\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n      setAllComande([]);\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const startTime = performance.now();\n\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n\n      const endTime = performance.now();\n      console.log(`📊 Statistiche caricate in ${(endTime - startTime).toFixed(2)}ms:`, stats);\n\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', err.response?.data || err.message);\n      // Imposta statistiche vuote in caso di errore\n      setStatistiche({\n        responsabili_attivi: 0,\n        totale_comande: 0,\n        comande_create: 0,\n        comande_in_corso: 0,\n        comande_completate: 0,\n        comande_annullate: 0\n      });\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([\n            loadResponsabili(),\n            loadComande(),\n            loadStatistiche()\n          ]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n\n        // Per comande di workflow (POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO) provenienti da visualizza cavi,\n        // apri l'inserimento dati specifico. Per altre comande, apri il dialog di modifica generale\n        const isWorkflowCommand = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comandaTrovata.tipo_comanda);\n\n        if (isWorkflowCommand) {\n          console.log(`📏 Apertura gestione per comanda ${comandaTrovata.tipo_comanda} da visualizza cavi:`, comandaParam);\n          handleOpenInserimentoMetri(comandaTrovata);\n        } else {\n          console.log('✏️ Apertura modifica per comanda non-workflow:', comandaParam);\n          handleOpenComandaDialog(comandaTrovata);\n        }\n\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      console.log('🚀 Caricamento comande per responsabili ottimizzato...');\n      const startTime = performance.now();\n\n      // OTTIMIZZAZIONE: Carica tutte le comande una sola volta invece di fare N chiamate\n      const allComande = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(allComande)) {\n        comandeArray = allComande;\n      } else if (allComande && Array.isArray(allComande.comande)) {\n        comandeArray = allComande.comande;\n      } else if (allComande && Array.isArray(allComande.data)) {\n        comandeArray = allComande.data;\n      }\n\n      // Raggruppa le comande per responsabile\n      const comandeMap = {};\n\n      // Inizializza tutti i responsabili con array vuoto\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n\n      // Raggruppa le comande per responsabile\n      comandeArray.forEach(comanda => {\n        const responsabile = responsabiliList.find(r => r.nome_responsabile === comanda.responsabile);\n        if (responsabile) {\n          comandeMap[responsabile.id_responsabile].push(comanda);\n        }\n      });\n\n      const endTime = performance.now();\n      console.log(`✅ Comande caricate in ${(endTime - startTime).toFixed(2)}ms (1 chiamata invece di ${responsabiliList.length})`);\n\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle comande:', err);\n      // Fallback: inizializza con array vuoti\n      const comandeMap = {};\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n      setComandePerResponsabile(comandeMap);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (comanda) => {\n    setSelectedComanda(comanda);\n\n    if (comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n      handleCloseComandaDialog();\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  // Gestione inserimento metri e collegamenti\n  const handleOpenInserimentoMetri = (comanda) => {\n    if (comanda.tipo_comanda === 'POSA') {\n      // Per POSA: apri dialog inserimento metri\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    } else if (['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda)) {\n      // Per COLLEGAMENTI: apri dialog collegamenti\n      setComandaPerCollegamenti(comanda);\n      setOpenCollegamenti(true);\n    } else {\n      // Per altri tipi: apri dialog inserimento metri (fallback)\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    }\n  };\n\n  const handleCloseInserimentoMetri = () => {\n    setOpenInserimentoMetri(false);\n    setComandaPerMetri(null);\n  };\n\n  const handleCloseCollegamenti = () => {\n    setOpenCollegamenti(false);\n    setComandaPerCollegamenti(null);\n  };\n\n  const handleSuccessInserimentoMetri = async (message) => {\n    console.log('✅ Successo inserimento metri:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([\n      loadResponsabili(),\n      loadComande(),\n      loadStatistiche()\n    ]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  const handleSuccessCollegamenti = async (message) => {\n    console.log('✅ Successo collegamenti:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([\n      loadResponsabili(),\n      loadComande(),\n      loadStatistiche()\n    ]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  // Gestione stampa comanda\n  const handlePrintComanda = (comanda) => {\n    console.log('🖨️ Stampa comanda:', comanda.codice_comanda);\n\n    // Per ora mostra un dialog di selezione formato\n    const formato = window.prompt(\n      `Seleziona il formato di stampa per la comanda ${comanda.codice_comanda}:\\n\\n` +\n      '1 - A4 (Formato standard)\\n' +\n      '2 - A3 (Formato esteso)\\n\\n' +\n      'Inserisci 1 o 2:',\n      '1'\n    );\n\n    if (formato === '1' || formato === '2') {\n      const formatoNome = formato === '1' ? 'A4' : 'A3';\n      console.log(`📄 Generazione comanda ${comanda.codice_comanda} in formato ${formatoNome}`);\n\n      // TODO: Implementare la generazione del PDF\n      alert(`Funzionalità in sviluppo!\\n\\nComanda: ${comanda.codice_comanda}\\nFormato: ${formatoNome}\\nTipo: ${comanda.tipo_comanda}\\nResponsabile: ${comanda.responsabile}\\nCavi: ${comanda.numero_cavi_assegnati || 0}`);\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n\n\n  if (loading || loadingComande) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {searchingComanda && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          🔍 Ricerca comanda {searchingComanda} in corso...\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Comande - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Gestione Responsabili e Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<PeopleIcon />}\n                onClick={() => setOpenResponsabiliPopup(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#f5f7fa',\n                  color: '#2196f3',\n                  border: '1px solid #2196f3',\n                  '&:hover': {\n                    backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                    borderColor: '#1976d2'\n                  }\n                }}\n              >\n                Lista Responsabili\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => handleOpenResponsabileDialog('create')}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Responsabile\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Lista Comande in stile tabella */}\n          {loadingComande ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : allComande.length === 0 ? (\n            <Paper\n              elevation={0}\n              sx={{\n                p: 6,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              }}\n            >\n              <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Nessuna comanda disponibile\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                Crea la prima comanda per iniziare a gestire i lavori\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Prima Comanda\n              </Button>\n            </Paper>\n          ) : (\n            <ComandeListTable\n              comande={allComande}\n              onEditComanda={handleOpenComandaDialog}\n              onDeleteComanda={handleDeleteComanda}\n              onInserimentoMetri={handleOpenInserimentoMetri}\n              onPrintComanda={handlePrintComanda}\n              loading={loadingComande}\n            />\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            Modifica Comanda {selectedComanda?.codice_comanda}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Stato: {selectedComanda?.stato} • Cavi assegnati: {selectedComanda?.numero_cavi_assegnati || 0}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              <Typography variant=\"body2\">\n                <strong>Attenzione:</strong> Modificare il tipo di comanda riassegnerà automaticamente tutti i cavi al nuovo tipo.\n                Assicurati che i cavi siano compatibili con il nuovo tipo di operazione.\n              </Typography>\n            </Alert>\n\n            <TextField\n              fullWidth\n              select\n              label=\"Tipo Comanda\"\n              value={formDataComanda.tipo_comanda}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n              margin=\"normal\"\n              sx={{ mb: 2 }}\n            >\n              <MenuItem value=\"POSA\">Posa</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n              <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n            </TextField>\n\n            <TextField\n              fullWidth\n              label=\"Descrizione\"\n              value={formDataComanda.descrizione}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={3}\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Responsabile\"\n              value={formDataComanda.responsabile}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Note Capo Cantiere\"\n              value={formDataComanda.note_capo_cantiere}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={2}\n              helperText=\"Istruzioni specifiche per il responsabile\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Data Scadenza\"\n              type=\"date\"\n              value={formDataComanda.data_scadenza}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n              margin=\"normal\"\n              InputLabelProps={{\n                shrink: true,\n              }}\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitComanda}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            Salva Modifiche\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={(response, successMessage) => {\n          console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n          // Mostra messaggio di successo se fornito\n          if (successMessage) {\n            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n            console.log('📢 Successo:', successMessage);\n          }\n\n          // Ricarica tutti i dati per aggiornare l'interfaccia\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n\n          console.log('✅ Interfaccia aggiornata');\n        }}\n      />\n\n      {/* Popup Lista Responsabili */}\n      <ResponsabiliListPopup\n        open={openResponsabiliPopup}\n        onClose={() => setOpenResponsabiliPopup(false)}\n        responsabili={responsabili}\n        comandePerResponsabile={comandePerResponsabile}\n        onEditResponsabile={(responsabile) => {\n          setOpenResponsabiliPopup(false);\n          handleOpenResponsabileDialog('edit', responsabile);\n        }}\n        onDeleteResponsabile={async (idResponsabile) => {\n          await handleDeleteResponsabile(idResponsabile);\n          setOpenResponsabiliPopup(false);\n        }}\n        loading={loadingResponsabili}\n        error={error}\n      />\n\n      {/* Dialog Inserimento Metri */}\n      <InserimentoMetriDialog\n        open={openInserimentoMetri}\n        onClose={handleCloseInserimentoMetri}\n        comanda={comandaPerMetri}\n        onSuccess={handleSuccessInserimentoMetri}\n      />\n\n      {/* Dialog Collegamenti */}\n      <CollegamentiDialog\n        open={openCollegamenti}\n        onClose={handleCloseCollegamenti}\n        comanda={comandaPerCollegamenti}\n        onSuccess={handleSuccessCollegamenti}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/C,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC8D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACwE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC0E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3E,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC;IAC/DgF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqF,eAAe,EAAEC,kBAAkB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuF,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAC;IACrDyF,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACkG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EAE1E,MAAMsG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFzC,iBAAiB,CAAC,IAAI,CAAC;MACvB0C,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE3D,UAAU,CAAC;MAC/D,MAAM4D,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAEnC,MAAMC,WAAW,GAAG,MAAMzE,cAAc,CAAC0E,UAAU,CAAChE,UAAU,CAAC;;MAE/D;MACA,IAAIiE,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;QAC9BE,YAAY,GAAGF,WAAW;MAC5B,CAAC,MAAM,IAAIA,WAAW,IAAIG,KAAK,CAACC,OAAO,CAACJ,WAAW,CAACK,OAAO,CAAC,EAAE;QAC5DH,YAAY,GAAGF,WAAW,CAACK,OAAO;MACpC,CAAC,MAAM,IAAIL,WAAW,IAAIG,KAAK,CAACC,OAAO,CAACJ,WAAW,CAACM,IAAI,CAAC,EAAE;QACzDJ,YAAY,GAAGF,WAAW,CAACM,IAAI;MACjC;MAEA,MAAMC,OAAO,GAAGT,WAAW,CAACC,GAAG,CAAC,CAAC;MACjCJ,OAAO,CAACC,GAAG,CAAC,KAAKM,YAAY,CAACM,MAAM,wBAAwB,CAACD,OAAO,GAAGV,SAAS,EAAEY,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAEjG1D,aAAa,CAACmD,YAAY,CAAC;IAC7B,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,mCAAmC,EAAEkE,GAAG,CAAC;MACvDjE,QAAQ,CAAC,sCAAsC,CAAC;MAChDM,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRE,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAM0D,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFhB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE3D,UAAU,CAAC;MACnE,MAAM4D,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAEnC,MAAMa,KAAK,GAAG,MAAMrF,cAAc,CAACsF,qBAAqB,CAAC5E,UAAU,CAAC;MAEpE,MAAMsE,OAAO,GAAGT,WAAW,CAACC,GAAG,CAAC,CAAC;MACjCJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAACW,OAAO,GAAGV,SAAS,EAAEY,OAAO,CAAC,CAAC,CAAC,KAAK,EAAEG,KAAK,CAAC;MAEvF/D,cAAc,CAAC+D,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MAAA,IAAAI,aAAA;MACZnB,OAAO,CAACnD,KAAK,CAAC,6CAA6C,EAAEkE,GAAG,CAAC;MACjEf,OAAO,CAACnD,KAAK,CAAC,oBAAoB,EAAE,EAAAsE,aAAA,GAAAJ,GAAG,CAACK,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcR,IAAI,KAAII,GAAG,CAACM,OAAO,CAAC;MACtE;MACAnE,cAAc,CAAC;QACboE,mBAAmB,EAAE,CAAC;QACtBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE,CAAC;QACjBC,gBAAgB,EAAE,CAAC;QACnBC,kBAAkB,EAAE,CAAC;QACrBC,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF9D,sBAAsB,CAAC,IAAI,CAAC;MAC5BhB,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM6D,IAAI,GAAG,MAAM9E,mBAAmB,CAACgG,uBAAuB,CAACvF,UAAU,CAAC;MAC1EsB,eAAe,CAAC+C,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMmB,0BAA0B,CAACnB,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOI,GAAG,EAAE;MAAA,IAAAgB,cAAA,EAAAC,mBAAA;MACZhC,OAAO,CAACnD,KAAK,CAAC,0CAA0C,EAAEkE,GAAG,CAAC;MAC9D,MAAMkB,YAAY,GAAG,EAAAF,cAAA,GAAAhB,GAAG,CAACK,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcpB,IAAI,cAAAqB,mBAAA,uBAAlBA,mBAAA,CAAoBE,MAAM,KAAInB,GAAG,CAACM,OAAO,IAAI,yCAAyC;MAC3GvE,QAAQ,CAAC,4CAA4CmF,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACRnE,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAID;EACApE,SAAS,CAAC,MAAM;IACd,MAAMyI,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI7F,UAAU,EAAE;QACdM,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAMwF,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClB7B,WAAW,CAAC,CAAC,EACbiB,eAAe,CAAC,CAAC,CAClB,CAAC;QACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;UACZf,OAAO,CAACnD,KAAK,CAAC,kCAAkC,EAAEkE,GAAG,CAAC;QACxD,CAAC,SAAS;UACRnE,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC;IAEDuF,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC7F,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA5C,SAAS,CAAC,MAAM;IACd,MAAM4I,YAAY,GAAG7F,YAAY,CAAC8F,GAAG,CAAC,SAAS,CAAC;IAChDvC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEqC,YAAY,CAAC;IAChEtC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5BtC,YAAY,EAAEA,YAAY,CAACkD,MAAM;MACjC9C,sBAAsB,EAAEyE,MAAM,CAACC,IAAI,CAAC1E,sBAAsB,CAAC,CAAC8C,MAAM;MAClElE,OAAO;MACPkB;IACF,CAAC,CAAC;;IAEF;IACA,IAAIyE,YAAY,IAAIA,YAAY,KAAKvF,gBAAgB,EAAE;MACrDC,mBAAmB,CAACsF,YAAY,CAAC;IACnC;IAEA,IAAIA,YAAY,IAAI3E,YAAY,CAACkD,MAAM,GAAG,CAAC,IAAI2B,MAAM,CAACC,IAAI,CAAC1E,sBAAsB,CAAC,CAAC8C,MAAM,GAAG,CAAC,EAAE;MAC7Fb,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAIyC,cAAc,GAAG,IAAI;MAEzB,KAAK,MAAMtD,YAAY,IAAIzB,YAAY,EAAE;QACvC,MAAMgF,WAAW,GAAG5E,sBAAsB,CAACqB,YAAY,CAACwD,eAAe,CAAC,IAAI,EAAE;QAC9E5C,OAAO,CAACC,GAAG,CAAC,mBAAmBb,YAAY,CAACX,iBAAiB,KAAKkE,WAAW,CAAC9B,MAAM,UAAU,CAAC;QAC/F6B,cAAc,GAAGC,WAAW,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAKT,YAAY,CAAC;QACzE,IAAII,cAAc,EAAE;UAClB1C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyC,cAAc,CAAC;UACjD;QACF;MACF;MAEA,IAAIA,cAAc,EAAE;QAClB1C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEqC,YAAY,CAAC;QACnEtF,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;QAE3B;QACA;QACA,MAAMgG,iBAAiB,GAAG,CAAC,MAAM,EAAE,uBAAuB,EAAE,qBAAqB,CAAC,CAACC,QAAQ,CAACP,cAAc,CAACxD,YAAY,CAAC;QAExH,IAAI8D,iBAAiB,EAAE;UACrBhD,OAAO,CAACC,GAAG,CAAC,oCAAoCyC,cAAc,CAACxD,YAAY,sBAAsB,EAAEoD,YAAY,CAAC;UAChHY,0BAA0B,CAACR,cAAc,CAAC;QAC5C,CAAC,MAAM;UACL1C,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEqC,YAAY,CAAC;UAC3Ea,uBAAuB,CAACT,cAAc,CAAC;QACzC;;QAEA;QACAU,UAAU,CAAC,MAAM;UACf1G,eAAe,CAAC2G,IAAI,IAAI;YACtB,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACF,IAAI,CAAC;YAC3CC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;YAC3B,OAAOF,SAAS;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLtD,OAAO,CAACyD,IAAI,CAAC,yBAAyB,EAAEnB,YAAY,CAAC;QACrDtC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtCtC,YAAY,CAAC+F,OAAO,CAACC,IAAI,IAAI;UAC3B,MAAMjD,OAAO,GAAG3C,sBAAsB,CAAC4F,IAAI,CAACf,eAAe,CAAC,IAAI,EAAE;UAClElC,OAAO,CAACgD,OAAO,CAACE,GAAG,IAAI;YACrB5D,OAAO,CAACC,GAAG,CAAC,OAAO2D,GAAG,CAACb,cAAc,KAAKY,IAAI,CAAClF,iBAAiB,GAAG,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAAC9B,OAAO,IAAI,CAACkB,mBAAmB,EAAE;UACpCmC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpDmD,UAAU,CAAC,MAAM;YACfxB,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM,IAAIU,YAAY,EAAE;MACvBtC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAACtD,OAAO,IAAI,CAACkB,mBAAmB,IAAIF,YAAY,CAACkD,MAAM,KAAK,CAAC,EAAE;QACjEb,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD2B,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACnF,YAAY,EAAEkB,YAAY,EAAEI,sBAAsB,EAAEpB,OAAO,EAAEkB,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExF,MAAMiE,0BAA0B,GAAG,MAAO+B,gBAAgB,IAAK;IAC7D,IAAI;MACF7D,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMC,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;;MAEnC;MACA,MAAMjD,UAAU,GAAG,MAAMvB,cAAc,CAAC0E,UAAU,CAAChE,UAAU,CAAC;;MAE9D;MACA,IAAIiE,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACtD,UAAU,CAAC,EAAE;QAC7BoD,YAAY,GAAGpD,UAAU;MAC3B,CAAC,MAAM,IAAIA,UAAU,IAAIqD,KAAK,CAACC,OAAO,CAACtD,UAAU,CAACuD,OAAO,CAAC,EAAE;QAC1DH,YAAY,GAAGpD,UAAU,CAACuD,OAAO;MACnC,CAAC,MAAM,IAAIvD,UAAU,IAAIqD,KAAK,CAACC,OAAO,CAACtD,UAAU,CAACwD,IAAI,CAAC,EAAE;QACvDJ,YAAY,GAAGpD,UAAU,CAACwD,IAAI;MAChC;;MAEA;MACA,MAAMmD,UAAU,GAAG,CAAC,CAAC;;MAErB;MACAD,gBAAgB,CAACH,OAAO,CAACtE,YAAY,IAAI;QACvC0E,UAAU,CAAC1E,YAAY,CAACwD,eAAe,CAAC,GAAG,EAAE;MAC/C,CAAC,CAAC;;MAEF;MACArC,YAAY,CAACmD,OAAO,CAACK,OAAO,IAAI;QAC9B,MAAM3E,YAAY,GAAGyE,gBAAgB,CAAChB,IAAI,CAACmB,CAAC,IAAIA,CAAC,CAACvF,iBAAiB,KAAKsF,OAAO,CAAC3E,YAAY,CAAC;QAC7F,IAAIA,YAAY,EAAE;UAChB0E,UAAU,CAAC1E,YAAY,CAACwD,eAAe,CAAC,CAACqB,IAAI,CAACF,OAAO,CAAC;QACxD;MACF,CAAC,CAAC;MAEF,MAAMnD,OAAO,GAAGT,WAAW,CAACC,GAAG,CAAC,CAAC;MACjCJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAACW,OAAO,GAAGV,SAAS,EAAEY,OAAO,CAAC,CAAC,CAAC,4BAA4B+C,gBAAgB,CAAChD,MAAM,GAAG,CAAC;MAE5H7C,yBAAyB,CAAC8F,UAAU,CAAC;IACvC,CAAC,CAAC,OAAO/C,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,yCAAyC,EAAEkE,GAAG,CAAC;MAC7D;MACA,MAAM+C,UAAU,GAAG,CAAC,CAAC;MACrBD,gBAAgB,CAACH,OAAO,CAACtE,YAAY,IAAI;QACvC0E,UAAU,CAAC1E,YAAY,CAACwD,eAAe,CAAC,GAAG,EAAE;MAC/C,CAAC,CAAC;MACF5E,yBAAyB,CAAC8F,UAAU,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAACC,IAAI,EAAE/E,YAAY,GAAG,IAAI,KAAK;IAClEhB,yBAAyB,CAAC+F,IAAI,CAAC;IAC/B7F,uBAAuB,CAACc,YAAY,CAAC;IAErC,IAAI+E,IAAI,KAAK,MAAM,IAAI/E,YAAY,EAAE;MACnCZ,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEW,YAAY,CAACX,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEU,YAAY,CAACV,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAES,YAAY,CAACT,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMkG,6BAA6B,GAAGA,CAAA,KAAM;IAC1ClG,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BxB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMuH,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFvH,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACyB,oBAAoB,CAACE,iBAAiB,CAAC6F,IAAI,CAAC,CAAC,EAAE;QAClDxH,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACyB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjE7B,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIqB,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAMtC,mBAAmB,CAAC0I,kBAAkB,CAACjI,UAAU,EAAEiC,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAMtC,mBAAmB,CAAC2I,kBAAkB,CAACnG,oBAAoB,CAACuE,eAAe,EAAErE,oBAAoB,CAAC;MAC1G;MAEA6F,6BAA6B,CAAC,CAAC;MAC/B,MAAMxC,gBAAgB,CAAC,CAAC;MACxB,MAAM7B,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,yBAAyB,EAAEkE,GAAG,CAAC;MAC7CjE,QAAQ,CAACiE,GAAG,CAACmB,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMuC,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAM/I,mBAAmB,CAACgJ,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAM9C,gBAAgB,CAAC,CAAC;MACxB,MAAM7B,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEkE,GAAG,CAAC;MAChDjE,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMqG,uBAAuB,GAAIY,OAAO,IAAK;IAC3ChF,kBAAkB,CAACgF,OAAO,CAAC;IAE3B,IAAIA,OAAO,EAAE;MACX9E,kBAAkB,CAAC;QACjBC,YAAY,EAAE6E,OAAO,CAAC7E,YAAY;QAClCC,WAAW,EAAE4E,OAAO,CAAC5E,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAE2E,OAAO,CAAC3E,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAE0E,OAAO,CAAC1E,aAAa,IAAI,EAAE;QAC1CC,kBAAkB,EAAEyE,OAAO,CAACzE,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAT,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMiG,wBAAwB,GAAGA,CAAA,KAAM;IACrCjG,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyF,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMnJ,cAAc,CAACoJ,aAAa,CAAClG,eAAe,CAACiE,cAAc,EAAE/D,eAAe,CAAC;MACnF8F,wBAAwB,CAAC,CAAC;MAC1B,MAAMlD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAM7B,WAAW,CAAC,CAAC;MACnB,MAAMiB,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,yBAAyB,EAAEkE,GAAG,CAAC;MAC7CjE,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAMmI,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACP,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAMhJ,cAAc,CAACuJ,aAAa,CAACD,aAAa,CAAC;MACjD,MAAMtD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAM7B,WAAW,CAAC,CAAC;MACnB,MAAMiB,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEkE,GAAG,CAAC;MAChDjE,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMoG,0BAA0B,GAAIa,OAAO,IAAK;IAC9C,IAAIA,OAAO,CAAC7E,YAAY,KAAK,MAAM,EAAE;MACnC;MACAQ,kBAAkB,CAACqE,OAAO,CAAC;MAC3BvE,uBAAuB,CAAC,IAAI,CAAC;IAC/B,CAAC,MAAM,IAAI,CAAC,uBAAuB,EAAE,qBAAqB,CAAC,CAACyD,QAAQ,CAACc,OAAO,CAAC7E,YAAY,CAAC,EAAE;MAC1F;MACAY,yBAAyB,CAACiE,OAAO,CAAC;MAClCnE,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACL;MACAF,kBAAkB,CAACqE,OAAO,CAAC;MAC3BvE,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;EAED,MAAM4F,2BAA2B,GAAGA,CAAA,KAAM;IACxC5F,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM2F,uBAAuB,GAAGA,CAAA,KAAM;IACpCzF,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMwF,6BAA6B,GAAG,MAAOjE,OAAO,IAAK;IACvDrB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEoB,OAAO,CAAC;IACrDvE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMsF,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClB7B,WAAW,CAAC,CAAC,EACbiB,eAAe,CAAC,CAAC,CAClB,CAAC;;IAEF;IACA;EACF,CAAC;EAED,MAAMuE,yBAAyB,GAAG,MAAOlE,OAAO,IAAK;IACnDrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,OAAO,CAAC;IAChDvE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMsF,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClB7B,WAAW,CAAC,CAAC,EACbiB,eAAe,CAAC,CAAC,CAClB,CAAC;;IAEF;IACA;EACF,CAAC;;EAED;EACA,MAAMwE,kBAAkB,GAAIzB,OAAO,IAAK;IACtC/D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE8D,OAAO,CAAChB,cAAc,CAAC;;IAE1D;IACA,MAAM0C,OAAO,GAAGd,MAAM,CAACe,MAAM,CAC3B,iDAAiD3B,OAAO,CAAChB,cAAc,OAAO,GAC9E,6BAA6B,GAC7B,6BAA6B,GAC7B,kBAAkB,EAClB,GACF,CAAC;IAED,IAAI0C,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtC,MAAME,WAAW,GAAGF,OAAO,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI;MACjDzF,OAAO,CAACC,GAAG,CAAC,0BAA0B8D,OAAO,CAAChB,cAAc,eAAe4C,WAAW,EAAE,CAAC;;MAEzF;MACAC,KAAK,CAAC,yCAAyC7B,OAAO,CAAChB,cAAc,cAAc4C,WAAW,WAAW5B,OAAO,CAAC7E,YAAY,mBAAmB6E,OAAO,CAAC3E,YAAY,WAAW2E,OAAO,CAAC8B,qBAAqB,IAAI,CAAC,EAAE,CAAC;IACtN;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE;IACpB,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAID,IAAIvJ,OAAO,IAAIU,cAAc,EAAE;IAC7B,oBACEjB,OAAA,CAACxC,GAAG;MAACwM,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EpK,OAAA,CAAC7B,gBAAgB;QAAAkM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACExK,OAAA,CAACxC,GAAG;IAACiN,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhBpK,OAAA,CAACxC,GAAG;MAACmN,EAAE,EAAE,CAAE;MAAAP,QAAA,eACTpK,OAAA,CAACvC,UAAU;QAACmN,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrEjK;MAAY;QAAAkK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAEL/J,KAAK,iBACJT,OAAA,CAAC9B,KAAK;MAAC6M,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnC3J;IAAK;MAAA4J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA7J,gBAAgB,iBACfX,OAAA,CAAC9B,KAAK;MAAC6M,QAAQ,EAAC,MAAM;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,GAAC,+BACjB,EAACzJ,gBAAgB,EAAC,cACvC;IAAA;MAAA0J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGA3J,WAAW,iBACVb,OAAA,CAACrC,KAAK;MAAC8M,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7CpK,OAAA,CAACzB,KAAK;QAAC0M,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnGpK,OAAA,CAACzB,KAAK;UAAC0M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDpK,OAAA,CAACjB,UAAU;YAAC+L,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CxK,OAAA,CAACxC,GAAG;YAAA4M,QAAA,gBACFpK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DvJ,WAAW,CAACqE,mBAAmB,IAAI;YAAC;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbxK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRxK,OAAA,CAACzB,KAAK;UAAC0M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDpK,OAAA,CAACnB,UAAU;YAACiM,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CxK,OAAA,CAACxC,GAAG;YAAA4M,QAAA,gBACFpK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DvJ,WAAW,CAACsE,cAAc,IAAI;YAAC;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbxK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRxK,OAAA,CAACzB,KAAK;UAAC0M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDpK,OAAA,CAACf,eAAe;YAAC6L,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDxK,OAAA,CAACxC,GAAG;YAAA4M,QAAA,gBACFpK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DvJ,WAAW,CAACwE,gBAAgB,IAAI;YAAC;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbxK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRxK,OAAA,CAACzB,KAAK;UAAC0M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDpK,OAAA,CAACb,YAAY;YAAC2L,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDxK,OAAA,CAACxC,GAAG;YAAA4M,QAAA,gBACFpK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DvJ,WAAW,CAACyE,kBAAkB,IAAI;YAAC;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACbxK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRxK,OAAA,CAACzB,KAAK;UAAC0M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDpK,OAAA,CAACxC,GAAG;YAACiN,EAAE,EAAE;cACPa,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBR,OAAO,EAAGnK,WAAW,CAACyE,kBAAkB,IAAIzE,WAAW,CAACsE,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3FtE,WAAW,CAACyE,kBAAkB,IAAIzE,WAAW,CAACsE,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpH6E,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACApK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1DvJ,WAAW,CAACsE,cAAc,GAAG,CAAC,GAAGsG,IAAI,CAACC,KAAK,CAAE7K,WAAW,CAACyE,kBAAkB,GAAGzE,WAAW,CAACsE,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNxK,OAAA,CAACxC,GAAG;YAAA4M,QAAA,gBACFpK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjDvJ,WAAW,CAACuE,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDxK,OAAA,CAACxC,GAAG;MAAA4M,QAAA,eACFpK,OAAA,CAACxC,GAAG;QAAA4M,QAAA,gBAEFpK,OAAA,CAACxC,GAAG;UAACwM,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3EpK,OAAA,CAACvC,UAAU;YAACmN,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxK,OAAA,CAACxC,GAAG;YAACwM,OAAO,EAAC,MAAM;YAAC2B,GAAG,EAAE,CAAE;YAAAvB,QAAA,gBACzBpK,OAAA,CAACtC,MAAM;cACLkN,OAAO,EAAC,UAAU;cAClBgB,SAAS,eAAE5L,OAAA,CAACX,UAAU;gBAAAgL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BqB,OAAO,EAAEA,CAAA,KAAMzK,wBAAwB,CAAC,IAAI,CAAE;cAC9CqJ,EAAE,EAAE;gBACFqB,aAAa,EAAE,MAAM;gBACrBjB,UAAU,EAAE,GAAG;gBACfkB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BnB,KAAK,EAAE,SAAS;gBAChBoB,MAAM,EAAE,mBAAmB;gBAC3B,SAAS,EAAE;kBACTD,eAAe,EAAE,yBAAyB;kBAC1CE,WAAW,EAAE;gBACf;cACF,CAAE;cAAA/B,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxK,OAAA,CAACtC,MAAM;cACLkN,OAAO,EAAC,WAAW;cACnBgB,SAAS,eAAE5L,OAAA,CAACrB,OAAO;gBAAA0L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBqB,OAAO,EAAEA,CAAA,KAAM/D,4BAA4B,CAAC,QAAQ,CAAE;cACtD2C,EAAE,EAAE;gBACFqB,aAAa,EAAE,MAAM;gBACrBjB,UAAU,EAAE,GAAG;gBACfkB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BnB,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTmB,eAAe,EAAE;gBACnB;cACF,CAAE;cAAA7B,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLvJ,cAAc,gBACbjB,OAAA,CAACxC,GAAG;UAACwM,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAAC+B,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAChDpK,OAAA,CAAC7B,gBAAgB;YAAAkM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJzJ,UAAU,CAAC0D,MAAM,KAAK,CAAC,gBACzBzE,OAAA,CAACrC,KAAK;UACJyO,SAAS,EAAE,CAAE;UACb3B,EAAE,EAAE;YACFC,CAAC,EAAE,CAAC;YACJ2B,SAAS,EAAE,QAAQ;YACnBJ,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAE;UACf,CAAE;UAAA/B,QAAA,gBAEFpK,OAAA,CAACnB,UAAU;YAAC4L,EAAE,EAAE;cAAEW,QAAQ,EAAE,EAAE;cAAEN,KAAK,EAAE,UAAU;cAAEH,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DxK,OAAA,CAACvC,UAAU;YAACmN,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,gBAAgB;YAACwB,YAAY;YAAAlC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxK,OAAA,CAACvC,UAAU;YAACmN,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAACL,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxK,OAAA,CAACtC,MAAM;YACLkN,OAAO,EAAC,WAAW;YACnBgB,SAAS,eAAE5L,OAAA,CAACrB,OAAO;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBqB,OAAO,EAAEA,CAAA,KAAMvK,kBAAkB,CAAC,IAAI,CAAE;YACxCmJ,EAAE,EAAE;cACFqB,aAAa,EAAE,MAAM;cACrBG,eAAe,EAAE,SAAS;cAC1BnB,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACTmB,eAAe,EAAE;cACnB;YACF,CAAE;YAAA7B,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAERxK,OAAA,CAACJ,gBAAgB;UACf0E,OAAO,EAAEvD,UAAW;UACpBwL,aAAa,EAAExF,uBAAwB;UACvCyF,eAAe,EAAE3D,mBAAoB;UACrC4D,kBAAkB,EAAE3F,0BAA2B;UAC/C4F,cAAc,EAAEtD,kBAAmB;UACnC7I,OAAO,EAAEU;QAAe;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxK,OAAA,CAACnC,MAAM;MACL8O,IAAI,EAAE9K,sBAAuB;MAC7B+K,OAAO,EAAE5E,6BAA8B;MACvC6E,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACVtC,EAAE,EAAE;UAAEe,YAAY,EAAE;QAAE;MACxB,CAAE;MAAApB,QAAA,gBAEFpK,OAAA,CAAClC,WAAW;QAAC2M,EAAE,EAAE;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA5C,QAAA,eACzBpK,OAAA,CAACvC,UAAU;UAACmN,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CrI,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAAsI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdxK,OAAA,CAACjC,aAAa;QAAAqM,QAAA,eACZpK,OAAA,CAACxC,GAAG;UAACiN,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBACjBpK,OAAA,CAAC/B,SAAS;YACR6O,SAAS;YACTI,KAAK,EAAC,mBAAmB;YACzBC,KAAK,EAAEhL,oBAAoB,CAACE,iBAAkB;YAC9C+K,QAAQ,EAAGC,CAAC,IAAKjL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAEgL,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzGI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACR5C,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFxK,OAAA,CAAC/B,SAAS;YACR6O,SAAS;YACTI,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,OAAO;YACZN,KAAK,EAAEhL,oBAAoB,CAACG,KAAM;YAClC8K,QAAQ,EAAGC,CAAC,IAAKjL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAE+K,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC7FI,MAAM,EAAC,QAAQ;YACf3C,OAAO,EAAC,UAAU;YAClB8C,UAAU,EAAC,uDAAuD;YAClEjD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFxK,OAAA,CAAC/B,SAAS;YACR6O,SAAS;YACTI,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAEhL,oBAAoB,CAACI,QAAS;YACrC6K,QAAQ,EAAGC,CAAC,IAAKjL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAE8K,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACf3C,OAAO,EAAC,UAAU;YAClB8C,UAAU,EAAC;UAA+C;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBxK,OAAA,CAAChC,aAAa;QAACyM,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA7C,QAAA,gBACjCpK,OAAA,CAACtC,MAAM;UACLmO,OAAO,EAAE7D,6BAA8B;UACvCyC,EAAE,EAAE;YAAEqB,aAAa,EAAE;UAAO,CAAE;UAAA1B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxK,OAAA,CAACtC,MAAM;UACLmO,OAAO,EAAE5D,wBAAyB;UAClC2C,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFqB,aAAa,EAAE,MAAM;YACrBjB,UAAU,EAAE,GAAG;YACfkB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EAEDrI,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAsI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTxK,OAAA,CAACnC,MAAM;MACL8O,IAAI,EAAEnK,iBAAkB;MACxBoK,OAAO,EAAElE,wBAAyB;MAClCmE,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACVtC,EAAE,EAAE;UAAEe,YAAY,EAAE;QAAE;MACxB,CAAE;MAAApB,QAAA,gBAEFpK,OAAA,CAAClC,WAAW;QAAC2M,EAAE,EAAE;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA5C,QAAA,gBACzBpK,OAAA,CAACvC,UAAU;UAACmN,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,GAAC,mBAC/B,EAAC1H,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiE,cAAc;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACbxK,OAAA,CAACvC,UAAU;UAACmN,OAAO,EAAC,OAAO;UAACE,KAAK,EAAC,gBAAgB;UAAAV,QAAA,GAAC,SAC1C,EAAC1H,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoH,KAAK,EAAC,0BAAmB,EAAC,CAAApH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+G,qBAAqB,KAAI,CAAC;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdxK,OAAA,CAACjC,aAAa;QAAAqM,QAAA,eACZpK,OAAA,CAACxC,GAAG;UAACiN,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBACjBpK,OAAA,CAAC9B,KAAK;YAAC6M,QAAQ,EAAC,SAAS;YAACN,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eACtCpK,OAAA,CAACvC,UAAU;cAACmN,OAAO,EAAC,OAAO;cAAAR,QAAA,gBACzBpK,OAAA;gBAAAoK,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,sKAE9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAERxK,OAAA,CAAC/B,SAAS;YACR6O,SAAS;YACTa,MAAM;YACNT,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAEvK,eAAe,CAACE,YAAa;YACpCsK,QAAQ,EAAGC,CAAC,IAAKxK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEE,YAAY,EAAEuK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC1FI,MAAM,EAAC,QAAQ;YACf9C,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBAEdpK,OAAA,CAACxB,QAAQ;cAAC2O,KAAK,EAAC,MAAM;cAAA/C,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtCxK,OAAA,CAACxB,QAAQ;cAAC2O,KAAK,EAAC,uBAAuB;cAAA/C,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxExK,OAAA,CAACxB,QAAQ;cAAC2O,KAAK,EAAC,qBAAqB;cAAA/C,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpExK,OAAA,CAACxB,QAAQ;cAAC2O,KAAK,EAAC,gBAAgB;cAAA/C,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAEZxK,OAAA,CAAC/B,SAAS;YACR6O,SAAS;YACTI,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAEvK,eAAe,CAACG,WAAY;YACnCqK,QAAQ,EAAGC,CAAC,IAAKxK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEG,WAAW,EAAEsK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzFI,MAAM,EAAC,QAAQ;YACfK,SAAS;YACTC,IAAI,EAAE,CAAE;YACRpD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFxK,OAAA,CAAC/B,SAAS;YACR6O,SAAS;YACTI,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAEvK,eAAe,CAACI,YAAa;YACpCoK,QAAQ,EAAGC,CAAC,IAAKxK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEI,YAAY,EAAEqK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC1FI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRE,UAAU,EAAC,0CAAuC;YAClDjD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFxK,OAAA,CAAC/B,SAAS;YACR6O,SAAS;YACTI,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAEvK,eAAe,CAACM,kBAAmB;YAC1CkK,QAAQ,EAAGC,CAAC,IAAKxK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEM,kBAAkB,EAAEmK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfK,SAAS;YACTC,IAAI,EAAE,CAAE;YACRH,UAAU,EAAC,2CAA2C;YACtDjD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFxK,OAAA,CAAC/B,SAAS;YACR6O,SAAS;YACTI,KAAK,EAAC,eAAe;YACrBO,IAAI,EAAC,MAAM;YACXN,KAAK,EAAEvK,eAAe,CAACK,aAAc;YACrCmK,QAAQ,EAAGC,CAAC,IAAKxK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEK,aAAa,EAAEoK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC3FI,MAAM,EAAC,QAAQ;YACfO,eAAe,EAAE;cACfC,MAAM,EAAE;YACV;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBxK,OAAA,CAAChC,aAAa;QAACyM,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA7C,QAAA,gBACjCpK,OAAA,CAACtC,MAAM;UACLmO,OAAO,EAAEnD,wBAAyB;UAClC+B,EAAE,EAAE;YAAEqB,aAAa,EAAE;UAAO,CAAE;UAAA1B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxK,OAAA,CAACtC,MAAM;UACLmO,OAAO,EAAElD,mBAAoB;UAC7BiC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFqB,aAAa,EAAE,MAAM;YACrBjB,UAAU,EAAE,GAAG;YACfkB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTxK,OAAA,CAACN,kBAAkB;MACjBQ,UAAU,EAAEA,UAAW;MACvByM,IAAI,EAAEtL,eAAgB;MACtBuL,OAAO,EAAEA,CAAA,KAAMtL,kBAAkB,CAAC,KAAK,CAAE;MACzC0M,SAAS,EAAEA,CAAChJ,QAAQ,EAAEiJ,cAAc,KAAK;QACvCrK,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAIoK,cAAc,EAAE;UAClB;UACArK,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoK,cAAc,CAAC;QAC7C;;QAEA;QACAtK,WAAW,CAAC,CAAC;QACbiB,eAAe,CAAC,CAAC;QACjBY,gBAAgB,CAAC,CAAC;QAClBlE,kBAAkB,CAAC,KAAK,CAAC;QAEzBsC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC;IAAE;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFxK,OAAA,CAACL,qBAAqB;MACpBgN,IAAI,EAAExL,qBAAsB;MAC5ByL,OAAO,EAAEA,CAAA,KAAMxL,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,YAAY,EAAEA,YAAa;MAC3BI,sBAAsB,EAAEA,sBAAuB;MAC/CuM,kBAAkB,EAAGlL,YAAY,IAAK;QACpC5B,wBAAwB,CAAC,KAAK,CAAC;QAC/B0G,4BAA4B,CAAC,MAAM,EAAE9E,YAAY,CAAC;MACpD,CAAE;MACFmL,oBAAoB,EAAE,MAAO7F,cAAc,IAAK;QAC9C,MAAMD,wBAAwB,CAACC,cAAc,CAAC;QAC9ClH,wBAAwB,CAAC,KAAK,CAAC;MACjC,CAAE;MACFb,OAAO,EAAEkB,mBAAoB;MAC7BhB,KAAK,EAAEA;IAAM;MAAA4J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGFxK,OAAA,CAACH,sBAAsB;MACrB8M,IAAI,EAAExJ,oBAAqB;MAC3ByJ,OAAO,EAAE5D,2BAA4B;MACrCrB,OAAO,EAAEtE,eAAgB;MACzB2K,SAAS,EAAE9E;IAA8B;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFxK,OAAA,CAACF,kBAAkB;MACjB6M,IAAI,EAAEpJ,gBAAiB;MACvBqJ,OAAO,EAAE3D,uBAAwB;MACjCtB,OAAO,EAAElE,sBAAuB;MAChCuK,SAAS,EAAE7E;IAA0B;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACpK,EAAA,CA58BIH,wBAAwB;EAAA,QAEY1C,eAAe;AAAA;AAAA6Q,EAAA,GAFnDnO,wBAAwB;AA88B9B,eAAeA,wBAAwB;AAAC,IAAAmO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}