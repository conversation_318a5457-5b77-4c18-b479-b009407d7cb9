#!/usr/bin/env python3
"""
Script di test per il sistema di calcolo resa oraria.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.resa_oraria import (
    calcola_resa_posa, calcola_resa_collegamento, calcola_resa_certificazione,
    registra_tempo_lavoro_cavo, calcola_resa_comanda, calcola_statistiche_resa_cantiere,
    genera_report_resa_oraria
)
from modules.database_pg import Database
import logging

# Configura logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def test_calcoli_resa_base():
    """Test dei calcoli di resa di base."""
    print("\n🧮 TEST CALCOLI RESA DI BASE")
    print("=" * 50)
    
    # Test resa posa
    print("\n📏 Test Resa Posa:")
    metri_posati = 150.0
    ore_lavoro = 6.0
    numero_persone = 3
    
    resa_totale, resa_per_persona = calcola_resa_posa(metri_posati, ore_lavoro, numero_persone)
    print(f"   Metri posati: {metri_posati}m")
    print(f"   Ore lavoro: {ore_lavoro}h")
    print(f"   Numero persone: {numero_persone}")
    print(f"   ✅ Resa totale: {resa_totale:.2f} m/h")
    print(f"   ✅ Resa per persona: {resa_per_persona:.2f} m/h/persona")
    
    # Test resa collegamento
    print("\n🔌 Test Resa Collegamento:")
    numero_collegamenti = 8
    ore_lavoro = 4.0
    numero_persone = 2
    
    resa_totale, resa_per_squadra = calcola_resa_collegamento(numero_collegamenti, ore_lavoro, numero_persone)
    print(f"   Collegamenti effettuati: {numero_collegamenti}")
    print(f"   Ore lavoro: {ore_lavoro}h")
    print(f"   Numero persone: {numero_persone}")
    print(f"   ✅ Resa totale: {resa_totale:.2f} coll/h")
    print(f"   ✅ Resa per squadra: {resa_per_squadra:.2f} coll/h/squadra")
    
    # Test resa certificazione
    print("\n🔍 Test Resa Certificazione:")
    numero_test = 12
    ore_lavoro = 3.0
    numero_persone = 2
    
    resa_totale, resa_per_squadra = calcola_resa_certificazione(numero_test, ore_lavoro, numero_persone)
    print(f"   Test effettuati: {numero_test}")
    print(f"   Ore lavoro: {ore_lavoro}h")
    print(f"   Numero persone: {numero_persone}")
    print(f"   ✅ Resa totale: {resa_totale:.2f} test/h")
    print(f"   ✅ Resa per squadra: {resa_per_squadra:.2f} test/h/squadra")

def test_registrazione_tempo():
    """Test della registrazione tempo di lavoro."""
    print("\n⏱️ TEST REGISTRAZIONE TEMPO LAVORO")
    print("=" * 50)
    
    # Parametri di test
    id_cavo = "CAV001"
    id_cantiere = 1
    
    # Test registrazione tempo posa
    print("\n📝 Registrazione tempo posa:")
    success = registra_tempo_lavoro_cavo(
        id_cavo=id_cavo,
        id_cantiere=id_cantiere,
        tipo_attivita="POSA",
        ore_lavoro=4.5,
        numero_persone=3
    )
    
    if success:
        print(f"   ✅ Tempo posa registrato per cavo {id_cavo}")
    else:
        print(f"   ❌ Errore nella registrazione tempo posa per cavo {id_cavo}")
    
    # Test registrazione tempo collegamento
    print("\n📝 Registrazione tempo collegamento partenza:")
    success = registra_tempo_lavoro_cavo(
        id_cavo=id_cavo,
        id_cantiere=id_cantiere,
        tipo_attivita="COLLEGAMENTO_PARTENZA",
        ore_lavoro=2.0,
        numero_persone=2
    )
    
    if success:
        print(f"   ✅ Tempo collegamento registrato per cavo {id_cavo}")
    else:
        print(f"   ❌ Errore nella registrazione tempo collegamento per cavo {id_cavo}")

def test_calcolo_resa_comanda():
    """Test del calcolo resa per comanda specifica."""
    print("\n📊 TEST CALCOLO RESA COMANDA")
    print("=" * 50)
    
    # Lista di comande di test (sostituire con comande reali dal database)
    comande_test = ["POS001", "CPT001", "CAR001", "CER001"]
    
    for codice_comanda in comande_test:
        print(f"\n🔍 Analisi comanda: {codice_comanda}")
        
        resa_data = calcola_resa_comanda(codice_comanda)
        
        if resa_data:
            tipo_attivita = resa_data.get('tipo_attivita', 'N/A')
            totale_cavi = resa_data.get('totale_cavi', 0)
            totale_ore = resa_data.get('totale_ore', 0)
            
            print(f"   Tipo attività: {tipo_attivita}")
            print(f"   Totale cavi: {totale_cavi}")
            print(f"   Totale ore: {totale_ore}")
            
            if tipo_attivita == 'POSA':
                resa_metri = resa_data.get('resa_media_metri_ora', 0)
                resa_persona = resa_data.get('resa_media_per_persona', 0)
                print(f"   ✅ Resa: {resa_metri:.2f} m/h ({resa_persona:.2f} m/h/persona)")
                
            elif tipo_attivita in ['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO']:
                resa_squadra = resa_data.get('resa_per_squadra', 0)
                print(f"   ✅ Resa: {resa_squadra:.2f} coll/h/squadra")
                
            elif tipo_attivita == 'CERTIFICAZIONE':
                resa_squadra = resa_data.get('resa_per_squadra', 0)
                conformita = resa_data.get('percentuale_conformita', 0)
                print(f"   ✅ Resa: {resa_squadra:.2f} test/h/squadra")
                print(f"   ✅ Conformità: {conformita:.1f}%")
        else:
            print(f"   ⚠️ Nessun dato di resa trovato per comanda {codice_comanda}")

def test_statistiche_cantiere():
    """Test delle statistiche resa cantiere."""
    print("\n📈 TEST STATISTICHE RESA CANTIERE")
    print("=" * 50)
    
    id_cantiere = 1
    
    print(f"\n🏗️ Analisi cantiere ID: {id_cantiere}")
    
    # Test statistiche ultimi 30 giorni
    statistiche = calcola_statistiche_resa_cantiere(id_cantiere, giorni_analisi=30)
    
    if statistiche:
        print(f"   Periodo analisi: {statistiche.get('periodo_analisi', 'N/A')}")
        
        statistiche_per_tipo = statistiche.get('statistiche_per_tipo', {})
        
        for tipo, stats in statistiche_per_tipo.items():
            print(f"\n   📋 {tipo}:")
            print(f"      Totale lavori: {stats.get('totale_lavori', 0)}")
            print(f"      Totale ore: {stats.get('totale_ore', 0):.2f}")
            
            if tipo == 'POSA':
                print(f"      Metri posati: {stats.get('totale_metri', 0):.2f}")
                print(f"      Resa media: {stats.get('resa_media_metri_ora', 0):.2f} m/h")
            elif tipo.startswith('COLLEGAMENTO'):
                print(f"      Collegamenti: {stats.get('totale_collegamenti', 0)}")
                print(f"      Resa media: {stats.get('resa_media_per_squadra', 0):.2f} coll/h")
            elif tipo == 'CERTIFICAZIONE':
                print(f"      Test effettuati: {stats.get('totale_test', 0)}")
                print(f"      Resa media: {stats.get('resa_media_per_squadra', 0):.2f} test/h")
                print(f"      Conformità: {stats.get('percentuale_conformita', 0):.1f}%")
        
        # Riepilogo generale
        riepilogo = statistiche.get('riepilogo_generale', {})
        if riepilogo:
            print(f"\n   📊 RIEPILOGO GENERALE:")
            print(f"      Totale lavori: {riepilogo.get('totale_lavori_completati', 0)}")
            print(f"      Totale ore: {riepilogo.get('totale_ore_lavorate', 0):.2f}")
            print(f"      Responsabili attivi: {riepilogo.get('responsabili_totali', 0)}")
    else:
        print(f"   ⚠️ Nessuna statistica trovata per cantiere {id_cantiere}")

def test_report_completo():
    """Test del report completo di resa oraria."""
    print("\n📄 TEST REPORT COMPLETO")
    print("=" * 50)
    
    id_cantiere = 1
    
    print(f"\n📋 Generazione report per cantiere {id_cantiere}")
    
    # Report cantiere completo
    report = genera_report_resa_oraria(id_cantiere)
    
    if 'errore' not in report:
        print(f"   ✅ Report generato con successo")
        print(f"   Tipo report: {report.get('tipo_report', 'N/A')}")
        print(f"   Data generazione: {report.get('data_generazione', 'N/A')}")
        
        # Mostra raccomandazioni se presenti
        raccomandazioni = report.get('raccomandazioni', [])
        if raccomandazioni:
            print(f"\n   💡 RACCOMANDAZIONI:")
            for i, raccomandazione in enumerate(raccomandazioni, 1):
                print(f"      {i}. {raccomandazione}")
    else:
        print(f"   ❌ Errore nella generazione report: {report.get('errore', 'N/A')}")

def test_verifica_database():
    """Verifica che il database sia configurato correttamente."""
    print("\n🗄️ VERIFICA DATABASE")
    print("=" * 50)
    
    try:
        db = Database()
        
        # Verifica connessione
        with db.get_connection() as conn:
            with conn.cursor() as cursor:
                # Verifica esistenza nuovi campi nella tabella Cavi
                cursor.execute("""
                    SELECT column_name FROM information_schema.columns
                    WHERE table_name = 'cavi' 
                    AND column_name IN ('ore_lavoro_posa', 'ore_lavoro_collegamento_partenza', 
                                       'ore_lavoro_collegamento_arrivo', 'ore_lavoro_certificazione')
                """)
                
                campi_trovati = [row[0] for row in cursor.fetchall()]
                
                print(f"   Campi resa oraria trovati: {len(campi_trovati)}/4")
                for campo in campi_trovati:
                    print(f"   ✅ {campo}")
                
                campi_mancanti = set(['ore_lavoro_posa', 'ore_lavoro_collegamento_partenza', 
                                    'ore_lavoro_collegamento_arrivo', 'ore_lavoro_certificazione']) - set(campi_trovati)
                
                if campi_mancanti:
                    print(f"   ⚠️ Campi mancanti: {list(campi_mancanti)}")
                    print(f"   💡 Eseguire aggiornamento database per aggiungere i campi mancanti")
                else:
                    print(f"   ✅ Tutti i campi per resa oraria sono presenti")
                    
    except Exception as e:
        print(f"   ❌ Errore nella verifica database: {str(e)}")

def main():
    """Funzione principale di test."""
    print("🚀 SISTEMA RESA ORARIA - TEST COMPLETO")
    print("=" * 60)
    
    # Esegui tutti i test
    test_verifica_database()
    test_calcoli_resa_base()
    test_registrazione_tempo()
    test_calcolo_resa_comanda()
    test_statistiche_cantiere()
    test_report_completo()
    
    print("\n" + "=" * 60)
    print("✅ TEST COMPLETATI")
    print("\n💡 Per utilizzare il sistema:")
    print("   1. Assicurarsi che il database sia aggiornato con i nuovi campi")
    print("   2. Registrare i tempi di lavoro per ogni cavo")
    print("   3. Utilizzare le API per calcolare rese e statistiche")
    print("   4. Generare report per analisi e ottimizzazioni")

if __name__ == "__main__":
    main()
