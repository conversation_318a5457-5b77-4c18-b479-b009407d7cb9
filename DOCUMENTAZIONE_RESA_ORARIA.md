# 📊 SISTEMA RESA ORARIA - DOCUMENTAZIONE COMPLETA

## 🎯 **PANORAMICA**

Il Sistema Resa Oraria è un modulo intelligente per il calcolo della produttività nelle comande di lavoro. Calcola automaticamente la resa oraria per:

- **POSA**: metri/ora per persona
- **COLLEGAMENTI**: collegamenti/ora per squadra  
- **CERTIFICAZIONI**: test/ora per squadra

## 🗄️ **STRUTTURA DATABASE**

### **Nuovi Campi Tabella CAVI**
```sql
-- Campi per calcolo resa oraria
ore_lavoro_posa REAL,                        -- Ore totali per la posa
data_inizio_lavoro TIMESTAMP,                -- Timestamp inizio lavoro
data_fine_lavoro TIMESTAMP,                  -- Timestamp fine lavoro
ore_lavoro_collegamento_partenza REAL,       -- Ore per collegamento partenza
ore_lavoro_collegamento_arrivo REAL,         -- Ore per collegamento arrivo
ore_lavoro_certificazione REAL               -- Ore per certificazione
```

### **Campi Esistenti U<PERSON>izzati**
```sql
numero_persone_impiegate INTEGER,            -- Numero persone nella squadra
metratura_reale REAL,                        -- Metri effettivamente posati
collegamenti INTEGER,                        -- Stato collegamenti (0,1,2,3)
stato_certificazione TEXT                    -- Stato certificazione
```

## 🧮 **LOGICHE DI CALCOLO**

### **1. RESA POSA**
```python
def calcola_resa_posa(metri_posati, ore_lavoro, numero_persone):
    resa_totale = metri_posati / ore_lavoro           # metri/ora totali
    resa_per_persona = resa_totale / numero_persone  # metri/ora per persona
    return resa_totale, resa_per_persona
```

**Esempio:**
- 150 metri posati in 6 ore con 3 persone
- Resa totale: 25 m/h
- Resa per persona: 8.33 m/h/persona

### **2. RESA COLLEGAMENTI**
```python
def calcola_resa_collegamento(numero_collegamenti, ore_lavoro, numero_persone):
    resa_totale = numero_collegamenti / ore_lavoro    # collegamenti/ora
    resa_per_squadra = resa_totale                   # per squadra
    return resa_totale, resa_per_squadra
```

**Esempio:**
- 8 collegamenti in 4 ore con squadra di 2 persone
- Resa per squadra: 2 coll/h/squadra

### **3. RESA CERTIFICAZIONI**
```python
def calcola_resa_certificazione(numero_test, ore_lavoro, numero_persone):
    resa_totale = numero_test / ore_lavoro           # test/ora
    resa_per_squadra = resa_totale                  # per squadra
    return resa_totale, resa_per_squadra
```

**Esempio:**
- 12 test in 3 ore con squadra di 2 persone
- Resa per squadra: 4 test/h/squadra

## 🔧 **API ENDPOINTS**

### **Registrazione Tempo Lavoro**
```http
POST /resa-oraria/cavi/{id_cantiere}/{id_cavo}/registra-tempo
```

**Body:**
```json
{
    "tipo_attivita": "POSA",
    "ore_lavoro": 4.5,
    "numero_persone": 3,
    "data_lavoro": "2024-01-15T08:00:00"
}
```

### **Resa Comanda Specifica**
```http
GET /resa-oraria/comande/{codice_comanda}/resa
```

**Risposta:**
```json
{
    "tipo_attivita": "POSA",
    "codice_comanda": "POS001",
    "responsabile": "Mario Rossi",
    "totale_cavi": 15,
    "totale_ore": 45.5,
    "resa_media": 85.2,
    "dettagli_specifici": {
        "totale_metri": 1280.5,
        "resa_media_metri_ora": 85.2,
        "resa_media_per_persona": 28.4,
        "numero_medio_persone": 3.0
    }
}
```

### **Statistiche Cantiere**
```http
GET /resa-oraria/cantieri/{id_cantiere}/statistiche-resa?giorni_analisi=30
```

### **Report Completo**
```http
GET /resa-oraria/cantieri/{id_cantiere}/report-resa
```

### **Benchmark e Confronti**
```http
GET /resa-oraria/cantieri/{id_cantiere}/benchmark
```

## 📈 **STATISTICHE E ANALISI**

### **Metriche Calcolate**

#### **Per POSA:**
- Resa media metri/ora
- Resa media per persona
- Migliore/peggiore resa
- Totale metri posati
- Efficienza squadre

#### **Per COLLEGAMENTI:**
- Resa media collegamenti/ora per squadra
- Numero collegamenti completati
- Tempo medio per collegamento
- Efficienza squadre specializzate

#### **Per CERTIFICAZIONI:**
- Resa media test/ora per squadra
- Percentuale di conformità
- Tempo medio per test
- Qualità del lavoro (conformi vs non conformi)

### **Analisi Temporali**
- Confronto ultimi 30/90 giorni
- Trend di miglioramento/peggioramento
- Identificazione picchi di produttività
- Analisi stagionalità

## 🎯 **OBIETTIVI SUGGERITI**

### **Benchmark Standard**
```python
obiettivi_resa = {
    'posa_metri_ora_obiettivo': 80.0,           # metri/ora
    'collegamenti_ora_obiettivo': 1.5,          # collegamenti/ora
    'test_ora_obiettivo': 1.0,                  # test/ora
    'conformita_percentuale_obiettivo': 95.0    # % conformità
}
```

### **Soglie di Valutazione**

#### **POSA:**
- 🔴 < 50 m/h: Sotto la media
- 🟡 50-80 m/h: Nella norma
- 🟢 > 80 m/h: Ottima resa

#### **COLLEGAMENTI:**
- 🔴 < 0.5 coll/h: Sotto la media
- 🟡 0.5-1.5 coll/h: Nella norma
- 🟢 > 1.5 coll/h: Ottima resa

#### **CERTIFICAZIONI:**
- 🔴 < 0.3 test/h: Sotto la media
- 🟡 0.3-1.0 test/h: Nella norma
- 🟢 > 1.0 test/h: Ottima resa

## 💡 **SISTEMA DI RACCOMANDAZIONI**

### **Raccomandazioni Automatiche**

#### **Per Resa Bassa:**
- "⚠️ Resa di posa sotto la media. Verificare eventuali difficoltà tecniche o logistiche."
- "💡 Considerare l'aumento del numero di persone nella squadra per migliorare l'efficienza."
- "🔍 Percentuale di conformità sotto il 90%. Analizzare cause di non conformità."

#### **Per Resa Alta:**
- "✅ Ottima resa di posa! Considerare questo team come riferimento."
- "✅ Eccellente resa di collegamento! Team molto efficiente."
- "✅ Perfetta conformità! Ottimo lavoro di installazione e test."

### **Raccomandazioni Cantiere:**
- "📉 Resa posa media bassa (65.2 m/h). Considerare ottimizzazioni logistiche."
- "🔧 Resa collegamenti bassa (0.6 coll/h). Verificare procedure e formazione."
- "👥 Pochi responsabili attivi. Considerare formazione di nuovi team leader."

## 🔄 **FLUSSO DI LAVORO**

### **1. Registrazione Tempi**
```python
# Durante l'esecuzione del lavoro
registra_tempo_lavoro_cavo(
    id_cavo="CAV001",
    id_cantiere=1,
    tipo_attivita="POSA",
    ore_lavoro=4.5,
    numero_persone=3
)
```

### **2. Calcolo Resa Comanda**
```python
# Al completamento della comanda
resa_data = calcola_resa_comanda("POS001")
```

### **3. Analisi Statistiche**
```python
# Analisi periodica
statistiche = calcola_statistiche_resa_cantiere(
    id_cantiere=1,
    giorni_analisi=30
)
```

### **4. Generazione Report**
```python
# Report completo con raccomandazioni
report = genera_report_resa_oraria(id_cantiere=1)
```

## 🛠️ **IMPLEMENTAZIONE**

### **1. Aggiornamento Database**
```sql
-- Aggiungere i nuovi campi alla tabella Cavi
ALTER TABLE Cavi ADD COLUMN ore_lavoro_posa REAL;
ALTER TABLE Cavi ADD COLUMN data_inizio_lavoro TIMESTAMP;
ALTER TABLE Cavi ADD COLUMN data_fine_lavoro TIMESTAMP;
ALTER TABLE Cavi ADD COLUMN ore_lavoro_collegamento_partenza REAL;
ALTER TABLE Cavi ADD COLUMN ore_lavoro_collegamento_arrivo REAL;
ALTER TABLE Cavi ADD COLUMN ore_lavoro_certificazione REAL;
```

### **2. Integrazione API**
```python
# Aggiungere al router principale
from .resa_oraria import router as resa_oraria_router
api_router.include_router(resa_oraria_router, prefix="/resa-oraria", tags=["resa-oraria"])
```

### **3. Frontend Integration**
```javascript
// Registrazione tempo lavoro
const registraTempo = async (idCavo, datiTempo) => {
    const response = await fetch(`/api/resa-oraria/cavi/${idCantiere}/${idCavo}/registra-tempo`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(datiTempo)
    });
    return response.json();
};

// Ottenere statistiche
const getStatistiche = async (idCantiere) => {
    const response = await fetch(`/api/resa-oraria/cantieri/${idCantiere}/statistiche-resa`);
    return response.json();
};
```

## 📊 **DASHBOARD E VISUALIZZAZIONI**

### **Grafici Suggeriti**
1. **Resa nel Tempo**: Linea temporale della produttività
2. **Confronto Squadre**: Bar chart delle rese per responsabile
3. **Distribuzione Attività**: Pie chart del tempo per tipo attività
4. **Trend Conformità**: Andamento percentuale conformità
5. **Heatmap Produttività**: Mappa di calore per giorni/ore

### **KPI Dashboard**
- Resa media giornaliera
- Obiettivi vs realtà
- Top performer
- Aree di miglioramento
- Previsioni completamento

## 🚀 **UTILIZZO PRATICO**

### **Per Project Manager**
- Monitoraggio produttività squadre
- Identificazione colli di bottiglia
- Pianificazione risorse ottimale
- Previsioni tempi completamento

### **Per Team Leader**
- Benchmark performance squadra
- Identificazione aree formazione
- Ottimizzazione procedure lavoro
- Motivazione team con obiettivi chiari

### **Per Analisti**
- Report dettagliati produttività
- Analisi trend storici
- Confronti inter-cantiere
- ROI ottimizzazioni implementate

---

## 🎯 **CONCLUSIONI**

Il Sistema Resa Oraria fornisce:

✅ **Calcoli intelligenti** per ogni tipo di attività
✅ **Statistiche aggregate** per analisi approfondite  
✅ **Raccomandazioni automatiche** per miglioramenti
✅ **API complete** per integrazione frontend
✅ **Benchmark e obiettivi** per motivazione squadre
✅ **Report dettagliati** per decision making

Il sistema è progettato per essere:
- **Scalabile**: Funziona con qualsiasi numero di cantieri
- **Flessibile**: Adattabile a diverse tipologie di lavoro
- **Intelligente**: Fornisce insights automatici
- **Integrato**: Si integra perfettamente con il sistema comande esistente
