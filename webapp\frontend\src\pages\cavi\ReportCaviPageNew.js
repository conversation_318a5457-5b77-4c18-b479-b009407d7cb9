import React, { useState, useEffect } from 'react';
import '../../styles/reports.css';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,

  Button,
  Chip,
  Alert,
  CircularProgress,

  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Assessment as AssessmentIcon,

  Timeline as TimelineIcon,
  List as ListIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,


  DateRange as DateRangeIcon,
  Cable as CableIcon,

  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ShowChart as ShowChartIcon
} from '@mui/icons-material';
import { useParams } from 'react-router-dom';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import reportService from '../../services/reportService';
import FilterableTable from '../../components/common/FilterableTable';
import EmptyState from '../../components/common/EmptyState';
import MetricCard from '../../components/common/MetricCard';


// Import dei componenti grafici
import ProgressChart from '../../components/charts/ProgressChart';
import BoqChart from '../../components/charts/BoqChart';
import TimelineChart from '../../components/charts/TimelineChart';

const ReportCaviPageNew = () => {
  const { cantiereId } = useParams();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedReportType, setSelectedReportType] = useState('progress');
  const [formData, setFormData] = useState({
    formato: 'video',
    data_inizio: '',
    data_fine: '',
    id_bobina: ''
  });

  // New state to store all report data
  const [reportsData, setReportsData] = useState({
    progress: null,
    boq: null,
    bobinaSpecifica: null,
    storicoBobine: null
  });

  // State per controllo visualizzazione grafici
  const [showCharts, setShowCharts] = useState(true);

  // State per storico bobine accordion
  const [expandedBobine, setExpandedBobine] = useState(new Set());

  // Load all basic reports on component mount
  useEffect(() => {
    const loadAllReports = async () => {
      setLoading(true);
      try {
        // Import certificazione service
        const certificazioneService = await import('../../services/certificazioneService');

        // Create individual promises that handle their own errors
        const progressPromise = reportService.getProgressReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading progress report:', err);
            return { content: null };
          });

        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading BOQ report:', err);
            return { content: null };
          });

        // Carica statistiche certificazioni
        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId)
          .catch(err => {
            console.error('Error loading certificazioni:', err);
            return [];
          });

        // Wait for all promises to resolve (they won't reject due to the catch handlers)
        const [progressData, boqData, certificazioniData] = await Promise.all([
          progressPromise,
          boqPromise,
          certificazioniPromise
        ]);

        // Aggiungi statistiche certificazioni ai dati del progress report
        if (progressData.content && certificazioniData) {
          const totaleCavi = progressData.content.totale_cavi || 0;
          const caviCertificati = certificazioniData.length;
          const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;

          // Calcola certificazioni di oggi
          const oggi = new Date().toDateString();
          const certificazioniOggi = certificazioniData.filter(cert =>
            new Date(cert.data_certificazione).toDateString() === oggi
          ).length;

          progressData.content.certificazioni = {
            totale: caviCertificati,
            percentuale: percentualeCertificazione,
            oggi: certificazioniOggi,
            rimanenti: totaleCavi - caviCertificati
          };
        }

        // Set the data for each report, even if some are null
        setReportsData({
          progress: progressData.content,
          boq: boqData.content,
          bobinaSpecifica: null,
          storicoBobine: null
        });

        // Only set error to null if we successfully loaded at least one report
        if (progressData.content || boqData.content) {
          setError(null);
        } else {
          setError('Errore nel caricamento dei report. Riprova più tardi.');
        }
      } catch (err) {
        // This catch block should rarely be hit due to the individual error handling above
        console.error('Unexpected error loading reports:', err);
        setError('Errore nel caricamento dei report. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    };

    if (cantiereId) {
      loadAllReports();
    }
  }, [cantiereId]);

  // Load storico bobine when selected
  useEffect(() => {
    if (cantiereId && selectedReportType === 'storico-bobine') {
      loadStoricoBobine();
    }
  }, [cantiereId, selectedReportType]);

  // Function to load storico bobine data
  const loadStoricoBobine = async () => {
    try {
      setLoading(true);
      setError(null);

      // Ottieni il token di autenticazione
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');
      }

      // Chiamata API reale per ottenere i dati dello storico bobine
      const response = await fetch(`http://localhost:8001/api/reports/${cantiereId}/storico-bobine?formato=video`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Sessione scaduta. Effettua nuovamente il login.');
        } else if (response.status === 404) {
          throw new Error('Cantiere non trovato o endpoint non disponibile.');
        } else if (response.status === 500) {
          throw new Error('Errore interno del server. Riprova più tardi.');
        } else {
          throw new Error(`Errore HTTP ${response.status}: ${response.statusText}`);
        }
      }

      const result = await response.json();

      if (result.success && result.content) {
        setReportsData(prev => ({
          ...prev,
          storicoBobine: result.content
        }));
        console.log('✅ Storico bobine caricato con successo:', result.content);
      } else {
        throw new Error(result.message || 'Errore nel caricamento dei dati');
      }
    } catch (err) {
      console.error('❌ Errore nel caricamento dello storico bobine:', err);

      // Gestione errori più specifica
      let errorMessage = 'Impossibile caricare lo storico bobine. ';

      if (err.message.includes('Failed to fetch') || err.message.includes('NetworkError')) {
        errorMessage += 'Verifica la connessione di rete e che il server sia attivo.';
      } else if (err.message.includes('Token') || err.message.includes('Sessione')) {
        errorMessage += err.message;
      } else {
        errorMessage += err.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Nuova funzione per generare report con formato specificato
  const generateReportWithFormat = async (reportType, format) => {
    try {
      setLoading(true);
      setError(null);

      let response;

      switch (reportType) {
        case 'progress':
          response = await reportService.getProgressReport(cantiereId, format);
          break;
        case 'boq':
          response = await reportService.getBillOfQuantities(cantiereId, format);
          break;

        case 'posa-periodo':
          if (!formData.data_inizio || !formData.data_fine) {
            setError('Seleziona le date di inizio e fine periodo');
            return;
          }
          response = await reportService.getPosaPerPeriodoReport(
            cantiereId,
            formData.data_inizio,
            formData.data_fine,
            format
          );
          break;
        default:
          throw new Error('Tipo di report non riconosciuto');
      }

      if (format === 'video') {
        // For special reports, update the specific report data
        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {
          setReportsData(prev => ({
            ...prev,
            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content
          }));
        }
      } else {
        // Per PDF/Excel, apri il link di download
        if (response.file_url) {
          window.open(response.file_url, '_blank');
        }
      }
    } catch (err) {
      console.error('Errore nella generazione del report:', err);
      setError(err.detail || err.message || 'Errore durante la generazione del report');
    } finally {
      setLoading(false);
    }
  };



  const handleGenerateReport = async () => {
    await generateReportWithFormat(dialogType, formData.formato);
    setOpenDialog(false);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
    setFormData({
      formato: 'video',
      data_inizio: '',
      data_fine: '',
      id_bobina: ''
    });
  };



  const renderProgressReport = (data) => (
    <Box>
      {/* Header con controlli migliorato */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
        p: 2,
        bgcolor: '#f8f9fa',
        borderRadius: 1,
        border: '1px solid #e0e0e0'
      }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50' }}>
          📊 Report Avanzamento Lavori
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Metriche Principali - Cards Moderne con MetricCard */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Totali"
            value={data.metri_totali}
            unit="m"
            subtitle="Lunghezza complessiva del progetto"
            gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
            size="large"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Posati"
            value={data.metri_posati}
            unit="m"
            subtitle={`${data.percentuale_avanzamento}% completato`}
            gradient="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
            progress={data.percentuale_avanzamento}
            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}
            trendValue={`${data.percentuale_avanzamento}%`}
            size="large"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Rimanenti"
            value={data.metri_da_posare}
            unit="m"
            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}
            gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
            size="large"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Media/Giorno"
            value={data.media_giornaliera || 0}
            unit="m"
            subtitle={
              data.giorni_stimati
                ? `${data.giorni_stimati} giorni lavorativi rimasti`
                : (data.media_giornaliera > 0
                    ? 'Calcolo in corso'
                    : 'Nessuna posa recente')
            }
            gradient="linear-gradient(135deg, #fa709a 0%, #fee140 100%)"
            size="large"
            tooltip={
              data.giorni_lavorativi_effettivi
                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`
                : 'Media giornaliera basata sui giorni di lavoro effettivo'
            }
          />
        </Grid>
      </Grid>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <ProgressChart data={data} />
        </Box>
      )}

      {/* Dettagli Performance - Cards Informative */}
      <Grid container spacing={4} sx={{ mb: 5 }}>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Stato Cavi
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>
                      {data.totale_cavi}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      Cavi Totali
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>
                      {data.cavi_posati}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      Cavi Posati ({data.percentuale_cavi}%)
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Progresso</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {data.percentuale_cavi}%
                  </Typography>
                </Box>
                <Box sx={{
                  width: '100%',
                  height: 8,
                  bgcolor: '#e0e0e0',
                  borderRadius: 4,
                  overflow: 'hidden'
                }}>
                  <Box sx={{
                    width: `${data.percentuale_cavi}%`,
                    height: '100%',
                    bgcolor: '#27ae60',
                    transition: 'width 0.3s ease'
                  }} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Box sx={{
                  bgcolor: '#9c27b0',
                  borderRadius: '50%',
                  p: 1,
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                    🔒
                  </Typography>
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Stato Certificazioni Cavi
                </Typography>
              </Box>

              {data.certificazioni ? (
                <>
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    <Grid item xs={6}>
                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>
                        <Typography variant="h4" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>
                          {data.certificazioni.totale}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#666' }}>
                          Certificati
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={6}>
                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>
                        <Typography variant="h4" sx={{ fontWeight: 700, color: '#856404', mb: 1 }}>
                          {data.certificazioni.rimanenti}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#666' }}>
                          Da Certificare
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  <Box sx={{ textAlign: 'center', mb: 2 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: '#9c27b0', mb: 1 }}>
                      {data.certificazioni.percentuale}%
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                      Completamento Certificazioni
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#999', fontSize: '0.75rem' }}>
                      {data.certificazioni.oggi} certificazioni completate oggi
                    </Typography>
                  </Box>

                  {/* Progress bar certificazioni */}
                  <Box sx={{
                    width: '100%',
                    height: 8,
                    bgcolor: '#e0e0e0',
                    borderRadius: 4,
                    overflow: 'hidden'
                  }}>
                    <Box sx={{
                      width: `${data.certificazioni.percentuale}%`,
                      height: '100%',
                      bgcolor: '#9c27b0',
                      transition: 'width 0.3s ease'
                    }} />
                  </Box>
                </>
              ) : (
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Nessuna certificazione disponibile
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>



      {/* Attività Recente - Design Migliorato */}
      {data.posa_recente && data.posa_recente.length > 0 && (
        <Card sx={{ border: '1px solid #e0e0e0' }}>
          <CardContent sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                📈 Attività Recente
              </Typography>
            </Box>

            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}
            <Grid container spacing={2}>
              {data.posa_recente.slice(0, 5).map((posa, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Box sx={{
                    p: 2,
                    border: '1px solid #e0e0e0',
                    borderRadius: 2,
                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',
                    transition: 'all 0.2s',
                    '&:hover': {
                      bgcolor: '#f5f5f5',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                    }
                  }}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                      {posa.data}
                    </Typography>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#2c3e50' }}>
                      {posa.metri}m
                    </Typography>
                    {index === 0 && (
                      <Chip
                        label="Più recente"
                        size="small"
                        sx={{
                          mt: 1,
                          bgcolor: '#3498db',
                          color: 'white',
                          fontSize: '0.7rem'
                        }}
                      />
                    )}
                  </Box>
                </Grid>
              ))}
            </Grid>

            {/* Link per vedere tutti i dati se ce ne sono di più */}
            {data.posa_recente.length > 5 && (
              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="body2" sx={{ color: '#3498db' }}>
                      Mostra tutti i {data.posa_recente.length} record
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <FilterableTable
                      data={data.posa_recente.map(posa => ({
                        data: posa.data,
                        metri: `${posa.metri}m`
                      }))}
                      columns={[
                        { field: 'data', headerName: 'Data', width: 200 },
                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }
                      ]}
                      pagination={true}
                      pageSize={10}
                    />
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
          </CardContent>
        </Card>
      )}
    </Box>
  );

  const renderBoqReport = (data) => (
    <Box>
      {/* Header migliorato */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 3,
        p: 2,
        bgcolor: '#f8f9fa',
        borderRadius: 1,
        border: '1px solid #e0e0e0'
      }}>
        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />
        <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50' }}>
          📋 Bill of Quantities - Distinta Materiali
        </Typography>
      </Box>

      {/* Grafici BOQ se disponibili */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <BoqChart data={data} />
        </Box>
      )}


    </Box>
  );
  const renderStoricoBobineReport = (data) => {
    const toggleBobina = (bobinaId) => {
      const newExpanded = new Set(expandedBobine);
      if (newExpanded.has(bobinaId)) {
        newExpanded.delete(bobinaId);
      } else {
        newExpanded.add(bobinaId);
      }
      setExpandedBobine(newExpanded);
    };

    const getStatoColor = (stato) => {
      switch (stato) {
        case 'Disponibile': return '#27ae60';
        case 'In Uso': return '#f39c12';
        case 'Esaurita': return '#e74c3c';
        default: return '#95a5a6';
      }
    };

    return (
      <Box>
        {/* Header */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 3,
          p: 2,
          bgcolor: '#f8f9fa',
          borderRadius: 1,
          border: '1px solid #e0e0e0'
        }}>
          <TimelineIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />
          <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50' }}>
            📦 Storico Bobine - Cavi Associati
          </Typography>
        </Box>

        {/* Lista Bobine con Accordion */}
        <Box sx={{ mb: 3 }}>
          {data.bobine && data.bobine.length > 0 ? data.bobine.map((bobina) => (
            <Paper key={bobina.id} sx={{ mb: 2, border: '1px solid #e0e0e0' }}>
              {/* Header Bobina */}
              <Box
                sx={{
                  p: 3,
                  cursor: 'pointer',
                  bgcolor: expandedBobine.has(bobina.id) ? '#f8f9fa' : 'white',
                  borderBottom: expandedBobine.has(bobina.id) ? '1px solid #e0e0e0' : 'none',
                  '&:hover': {
                    bgcolor: '#f5f5f5'
                  }
                }}
                onClick={() => toggleBobina(bobina.id)}
              >
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={3}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: getStatoColor(bobina.stato),
                        mr: 2
                      }} />
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                          {bobina.codice}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#666' }}>
                          {bobina.tipologia}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={2}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                      Formazione
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {bobina.formazione}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={2}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                      Metri Totali
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {bobina.metri_totali}m
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={2}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                      Utilizzati
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500, color: '#e74c3c' }}>
                      {bobina.metri_utilizzati}m
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={2}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                      Residui
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500, color: '#27ae60' }}>
                      {bobina.metri_residui}m
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={1}>
                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      {expandedBobine.has(bobina.id) ?
                        <ExpandLessIcon sx={{ color: '#666' }} /> :
                        <ExpandMoreIcon sx={{ color: '#666' }} />
                      }
                    </Box>
                  </Grid>
                </Grid>
              </Box>

              {/* Dettagli Cavi Associati */}
              {expandedBobine.has(bobina.id) && (
                <Box sx={{ p: 3, bgcolor: '#fafafa' }}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: '#2c3e50' }}>
                    Cavi Associati ({bobina.cavi_associati.length})
                  </Typography>

                  {bobina.cavi_associati.length > 0 ? (
                    <Grid container spacing={2}>
                      {bobina.cavi_associati.map((cavo) => (
                        <Grid item xs={12} sm={6} md={4} key={cavo.id}>
                          <Paper sx={{ p: 2, border: '1px solid #e0e0e0', bgcolor: 'white' }}>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                              {cavo.nomenclatura}
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                              Metri utilizzati: <strong>{cavo.metri_utilizzati}m</strong>
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#666' }}>
                              Data posa: {new Date(cavo.data_posa).toLocaleDateString('it-IT')}
                            </Typography>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'white', borderRadius: 1 }}>
                      <Typography variant="body2" sx={{ color: '#666' }}>
                        Nessun cavo associato a questa bobina
                      </Typography>
                    </Box>
                  )}

                  {/* Info aggiuntive bobina */}
                  <Box sx={{ mt: 3, p: 2, bgcolor: 'white', borderRadius: 1, border: '1px solid #e0e0e0' }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                          Fornitore
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {bobina.fornitore}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                          Data Arrivo
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {new Date(bobina.data_arrivo).toLocaleDateString('it-IT')}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                          Stato
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            bgcolor: getStatoColor(bobina.stato),
                            mr: 1
                          }} />
                          <Typography variant="body1" sx={{ fontWeight: 500 }}>
                            {bobina.stato}
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Box>
              )}
            </Paper>
          )) : (
            <Paper sx={{ p: 4, textAlign: 'center', border: '1px solid #e0e0e0' }}>
              <Typography variant="h6" sx={{ color: '#666', mb: 2 }}>
                📦 Nessuna bobina trovata
              </Typography>
              <Typography variant="body2" sx={{ color: '#999' }}>
                Non ci sono bobine disponibili per questo cantiere.
              </Typography>
            </Paper>
          )}
        </Box>

        {/* Riepilogo */}
        {data.bobine && data.bobine.length > 0 && (
          <Paper sx={{ p: 3, bgcolor: '#f8f9fa', border: '1px solid #e0e0e0' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: '#2c3e50' }}>
              📊 Riepilogo Generale
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>
                    {data.bobine.length}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Bobine Totali
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>
                    {data.bobine.reduce((sum, b) => sum + (b.cavi_associati?.length || 0), 0)}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Cavi Associati
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>
                    {data.bobine.reduce((sum, b) => sum + (b.metri_utilizzati || 0), 0)}m
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Metri Utilizzati
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>
                    {data.bobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0)}m
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Metri Residui
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        )}
      </Box>
    );
  };





  const renderPosaPeriodoReport = (data) => (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'warning.main' }}>
          Report Posa per Periodo
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Statistiche Periodo - Layout Orizzontale */}
      <Grid container spacing={4} sx={{ mb: 5 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.totale_metri_periodo}m
            </Typography>
            <Typography variant="body1">Metri Totali</Typography>
            <Typography variant="caption">{data.data_inizio} - {data.data_fine}</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.giorni_attivi}
            </Typography>
            <Typography variant="body1">Giorni Attivi</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.media_giornaliera}m
            </Typography>
            <Typography variant="body1">Media/Giorno</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m
            </Typography>
            <Typography variant="body1">Media/Settimana</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 5, width: '100%' }}>
          <TimelineChart data={data} />
        </Box>
      )}

      {/* Posa Giornaliera */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Dettaglio Posa Giornaliera
        </Typography>
        <FilterableTable
          data={data.posa_giornaliera || []}
          columns={[
            { field: 'data', headerName: 'Data', width: 200 },
            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri}m` }
          ]}
          pageSize={10}
        />
      </Paper>
    </Box>
  );



  const renderDialog = () => (
    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
      <DialogTitle>
        {dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'}
      </DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Formato</InputLabel>
              <Select
                value={formData.formato}
                label="Formato"
                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}
              >
                <MenuItem value="video">Visualizza a schermo</MenuItem>
                <MenuItem value="pdf">Download PDF</MenuItem>
                <MenuItem value="excel">Download Excel</MenuItem>
              </Select>
            </FormControl>
          </Grid>



          {dialogType === 'posa-periodo' && (
            <>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Inizio"
                  value={formData.data_inizio}
                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Fine"
                  value={formData.data_fine}
                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCloseDialog}>Annulla</Button>
        <Button
          onClick={handleGenerateReport}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}
        >
          {loading ? 'Generazione...' : 'Genera Report'}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box className="report-main-container report-fade-in">
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>
        <AdminHomeButton />
      </Box>

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Reports Navigation */}
      <Box sx={{ mt: 3 }}>
        {/* Report Navigation - Design Compatto */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>
            🎯 Seleziona il tipo di report
          </Typography>
          <Grid container spacing={2}>
            {/* Report Avanzamento */}
            <Grid item xs={6} sm={4} md={3}>
              <Card
                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}
                sx={{
                  height: '140px',
                  cursor: 'pointer',
                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',
                  transition: 'all 0.2s'
                }}
                onClick={() => setSelectedReportType('progress')}
              >
                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
                    Avanzamento
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: '0.9rem' }}>
                    Panoramica lavori
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Bill of Quantities */}
            <Grid item xs={6} sm={4} md={3}>
              <Card
                sx={{
                  height: '140px',
                  cursor: 'pointer',
                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',
                  transition: 'all 0.2s'
                }}
                onClick={() => setSelectedReportType('boq')}
              >
                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
                    Bill of Quantities
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: '0.9rem' }}>
                    Distinta materiali
                  </Typography>
                </CardContent>
              </Card>
            </Grid>



            {/* Storico Bobine */}
            <Grid item xs={6} sm={4} md={3}>
              <Card
                sx={{
                  height: '140px',
                  cursor: 'pointer',
                  border: selectedReportType === 'storico-bobine' ? '2px solid #9b59b6' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'storico-bobine' ? '#f8f4ff' : 'white',
                  transition: 'all 0.2s'
                }}
                onClick={() => setSelectedReportType('storico-bobine')}
              >
                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
                    Storico Bobine
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: '0.9rem' }}>
                    Cavi per bobina
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* Report Content */}
        <Box sx={{ minHeight: '400px', width: '100%' }}>
          {/* Progress Report */}
          {selectedReportType === 'progress' && (
            <Paper sx={{ p: 2, borderRadius: 2 }}>
              {reportsData.progress ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderProgressReport(reportsData.progress)}
                </Box>
              ) : loading ? (
                <EmptyState
                  type="loading"
                  reportType="progress"
                  title="Caricamento Report Avanzamento..."
                  description="Stiamo elaborando i dati dell'avanzamento dei lavori"
                />
              ) : (
                <EmptyState
                  type="error"
                  reportType="progress"
                  title="Errore nel caricamento"
                  description="Impossibile caricare il report di avanzamento. Verifica la connessione e riprova."
                  onRetry={() => {
                    setLoading(true);
                    reportService.getProgressReport(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          progress: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying progress report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                  loading={loading}
                />
              )}
            </Paper>
          )}

          {/* Bill of Quantities */}
          {selectedReportType === 'boq' && (
            <Paper sx={{ p: 2, borderRadius: 2 }}>
              {reportsData.boq ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('boq', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('boq', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderBoqReport(reportsData.boq)}
                </Box>
              ) : loading ? (
                <EmptyState
                  type="loading"
                  reportType="boq"
                  title="Caricamento Bill of Quantities..."
                  description="Stiamo elaborando la distinta materiali"
                />
              ) : (
                <EmptyState
                  type="error"
                  reportType="boq"
                  title="Errore nel caricamento"
                  description="Impossibile caricare la distinta materiali. Verifica la connessione e riprova."
                  onRetry={() => {
                    setLoading(true);
                    reportService.getBillOfQuantities(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          boq: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying BOQ report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                  loading={loading}
                />
              )}
            </Paper>
          )}

          {/* Storico Bobine Report */}
          {selectedReportType === 'storico-bobine' && (
            <Paper sx={{ p: 2, borderRadius: 2 }}>
              {reportsData.storicoBobine ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('storico-bobine', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('storico-bobine', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderStoricoBobineReport(reportsData.storicoBobine)}
                </Box>
              ) : loading ? (
                <EmptyState
                  type="loading"
                  reportType="storico-bobine"
                  title="Caricamento Storico Bobine..."
                  description="Stiamo elaborando i dati delle bobine e dei cavi associati"
                />
              ) : (
                <EmptyState
                  type="error"
                  reportType="storico-bobine"
                  title="Errore nel caricamento"
                  description="Impossibile caricare lo storico bobine. Verifica la connessione e riprova."
                  onRetry={() => loadStoricoBobine()}
                  loading={loading}
                />
              )}
            </Paper>
          )}

          {/* Posa per Periodo Report */}
          {selectedReportType === 'posa-periodo' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.posaPeriodo ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}
                </Box>
              ) : (
                <EmptyState
                  type="action-required"
                  reportType="posa-periodo"
                  title="Seleziona un Periodo"
                  description="Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team."
                  actionLabel="Seleziona Periodo"
                  onAction={() => {
                    setDialogType('posa-periodo');
                    // Set default date range (last month to today)
                    const today = new Date();
                    const lastMonth = new Date();
                    lastMonth.setMonth(today.getMonth() - 1);

                    setFormData({
                      ...formData,
                      data_inizio: lastMonth.toISOString().split('T')[0],
                      data_fine: today.toISOString().split('T')[0]
                    });
                    setOpenDialog(true);
                  }}
                />
              )}
            </Paper>
          )}
        </Box>
      </Box>

      {/* Dialog per configurazione report */}
      {renderDialog()}
    </Box>
  );
};

export default ReportCaviPageNew;
