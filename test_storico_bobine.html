<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Storico Bobine - Risoluzione Errore</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .data-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .fix-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .fix-info h4 {
            margin-top: 0;
            color: #856404;
        }
        
        .endpoint-test {
            display: flex;
            gap: 10px;
            align-items: center;
            margin: 10px 0;
        }
        
        .endpoint-test input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Test Risoluzione Errore Storico Bobine</h1>
            <p>Verifica che l'endpoint funzioni correttamente dopo la correzione</p>
        </div>

        <div class="fix-info">
            <h4>✅ Problema Risolto:</h4>
            <p><strong>Errore:</strong> "Impossibile caricare lo storico bobine. Verifica la connessione e riprova."</p>
            <p><strong>Causa:</strong> URL endpoint sbagliato - usava porta 8003 invece di 8001</p>
            <p><strong>Soluzione:</strong> Corretto URL da <code>localhost:8003</code> a <code>localhost:8001</code></p>
        </div>

        <div class="test-section">
            <h3>🌐 Test Endpoint Storico Bobine</h3>
            
            <div class="endpoint-test">
                <label>Cantiere ID:</label>
                <input type="number" id="cantiereId" value="1" min="1">
                <button onclick="testStoricoBobine()">Test Endpoint</button>
            </div>
            
            <div id="endpointStatus" class="status loading" style="display: none;">
                Caricamento in corso...
            </div>
            
            <div id="endpointData" class="data-display" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Test Connessione Backend</h3>
            <button onclick="testBackendHealth()">Verifica Backend</button>
            <div id="backendStatus" class="status loading" style="display: none;">
                Verifica in corso...
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Checklist Risoluzione</h3>
            <ul>
                <li>✅ <strong>URL Endpoint:</strong> Corretto da localhost:8003 a localhost:8001</li>
                <li>✅ <strong>Autenticazione:</strong> Aggiunto header Authorization Bearer</li>
                <li>✅ <strong>Gestione Errori:</strong> Migliorata con messaggi specifici</li>
                <li>✅ <strong>Timeout:</strong> Gestito correttamente</li>
                <li>✅ <strong>Logging:</strong> Aggiunto per debug</li>
            </ul>
        </div>
    </div>

    <script>
        async function testStoricoBobine() {
            const cantiereId = document.getElementById('cantiereId').value;
            const statusDiv = document.getElementById('endpointStatus');
            const dataDiv = document.getElementById('endpointData');
            
            statusDiv.style.display = 'block';
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'Caricamento storico bobine...';
            dataDiv.style.display = 'none';
            
            try {
                // Simula il token (in produzione verrebbe dal localStorage)
                const mockToken = 'test-token';
                
                const response = await fetch(`http://localhost:8001/api/reports/${cantiereId}/storico-bobine?formato=video`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${mockToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    ✅ <strong>Successo!</strong><br>
                    Endpoint funzionante - Dati caricati correttamente<br>
                    Bobine trovate: ${result.content?.totale_bobine || 0}
                `;
                
                dataDiv.style.display = 'block';
                dataDiv.textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    ❌ <strong>Errore:</strong><br>
                    ${error.message}<br>
                    <small>Verifica che il backend sia in esecuzione su porta 8001</small>
                `;
                
                dataDiv.style.display = 'block';
                dataDiv.textContent = `Errore: ${error.message}`;
            }
        }
        
        async function testBackendHealth() {
            const statusDiv = document.getElementById('backendStatus');
            
            statusDiv.style.display = 'block';
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'Verifica connessione backend...';
            
            try {
                const response = await fetch('http://localhost:8001/docs', {
                    method: 'GET'
                });
                
                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        ✅ <strong>Backend Online!</strong><br>
                        Server FastAPI in esecuzione su porta 8001<br>
                        <a href="http://localhost:8001/docs" target="_blank">Apri Swagger UI</a>
                    `;
                } else {
                    throw new Error(`Backend risponde con status ${response.status}`);
                }
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    ❌ <strong>Backend Non Raggiungibile:</strong><br>
                    ${error.message}<br>
                    <small>Avvia il backend con: <code>python -m uvicorn backend.main:app --port 8001 --reload</code></small>
                `;
            }
        }
        
        // Test automatico all'avvio
        window.onload = function() {
            testBackendHealth();
        };
    </script>
</body>
</html>
