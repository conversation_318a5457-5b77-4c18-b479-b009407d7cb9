{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Button, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, List, ListItem, ListItemText, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Assignment as AssignIcon, Person as PersonIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, People as PeopleIcon, Construction as ConstructionIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\nimport CollegamentiDialog from './CollegamentiDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n\n  // Stati per dialog collegamenti\n  const [openCollegamenti, setOpenCollegamenti] = useState(false);\n  const [comandaPerCollegamenti, setComandaPerCollegamenti] = useState(null);\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const comandeData = await comandeService.getComande(cantiereId);\n      console.log('📋 Dati comande ricevuti:', comandeData);\n      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      var _err$response;\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n\n        // Per comande di workflow (POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO) provenienti da visualizza cavi,\n        // apri l'inserimento dati specifico. Per altre comande, apri il dialog di modifica generale\n        const isWorkflowCommand = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comandaTrovata.tipo_comanda);\n        if (isWorkflowCommand) {\n          console.log(`📏 Apertura gestione per comanda ${comandaTrovata.tipo_comanda} da visualizza cavi:`, comandaParam);\n          handleOpenInserimentoMetri(comandaTrovata);\n        } else {\n          console.log('✏️ Apertura modifica per comanda non-workflow:', comandaParam);\n          handleOpenComandaDialog(comandaTrovata);\n        }\n\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      console.log('🚀 Caricamento comande per responsabili ottimizzato...');\n      const startTime = performance.now();\n\n      // OTTIMIZZAZIONE: Carica tutte le comande una sola volta invece di fare N chiamate\n      const allComande = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(allComande)) {\n        comandeArray = allComande;\n      } else if (allComande && Array.isArray(allComande.comande)) {\n        comandeArray = allComande.comande;\n      } else if (allComande && Array.isArray(allComande.data)) {\n        comandeArray = allComande.data;\n      }\n\n      // Raggruppa le comande per responsabile\n      const comandeMap = {};\n\n      // Inizializza tutti i responsabili con array vuoto\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n\n      // Raggruppa le comande per responsabile\n      comandeArray.forEach(comanda => {\n        const responsabile = responsabiliList.find(r => r.nome_responsabile === comanda.responsabile);\n        if (responsabile) {\n          comandeMap[responsabile.id_responsabile].push(comanda);\n        }\n      });\n      const endTime = performance.now();\n      console.log(`✅ Comande caricate in ${(endTime - startTime).toFixed(2)}ms (1 chiamata invece di ${responsabiliList.length})`);\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle comande:', err);\n      // Fallback: inizializza con array vuoti\n      const comandeMap = {};\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n      setComandePerResponsabile(comandeMap);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = comanda => {\n    setSelectedComanda(comanda);\n    if (comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n      handleCloseComandaDialog();\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  // Gestione inserimento metri e collegamenti\n  const handleOpenInserimentoMetri = comanda => {\n    if (comanda.tipo_comanda === 'POSA') {\n      // Per POSA: apri dialog inserimento metri\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    } else if (['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda)) {\n      // Per COLLEGAMENTI: apri dialog collegamenti\n      setComandaPerCollegamenti(comanda);\n      setOpenCollegamenti(true);\n    } else {\n      // Per altri tipi: apri dialog inserimento metri (fallback)\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    }\n  };\n  const handleCloseInserimentoMetri = () => {\n    setOpenInserimentoMetri(false);\n    setComandaPerMetri(null);\n  };\n  const handleCloseCollegamenti = () => {\n    setOpenCollegamenti(false);\n    setComandaPerCollegamenti(null);\n  };\n  const handleSuccessInserimentoMetri = async message => {\n    console.log('✅ Successo inserimento metri:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n  const handleSuccessCollegamenti = async message => {\n    console.log('✅ Successo collegamenti:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  // Gestione stampa comanda\n  const handlePrintComanda = comanda => {\n    console.log('🖨️ Stampa comanda:', comanda.codice_comanda);\n\n    // Per ora mostra un dialog di selezione formato\n    const formato = window.prompt(`Seleziona il formato di stampa per la comanda ${comanda.codice_comanda}:\\n\\n` + '1 - A4 (Formato standard)\\n' + '2 - A3 (Formato esteso)\\n\\n' + 'Inserisci 1 o 2:', '1');\n    if (formato === '1' || formato === '2') {\n      const formatoNome = formato === '1' ? 'A4' : 'A3';\n      console.log(`📄 Generazione comanda ${comanda.codice_comanda} in formato ${formatoNome}`);\n\n      // TODO: Implementare la generazione del PDF\n      alert(`Funzionalità in sviluppo!\\n\\nComanda: ${comanda.codice_comanda}\\nFormato: ${formatoNome}\\nTipo: ${comanda.tipo_comanda}\\nResponsabile: ${comanda.responsabile}\\nCavi: ${comanda.numero_cavi_assegnati || 0}`);\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading || loadingComande) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 9\n    }, this), searchingComanda && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"\\uD83D\\uDD0D Ricerca comanda \", searchingComanda, \" in corso...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Gestione Responsabili e Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 28\n              }, this),\n              onClick: () => setOpenResponsabiliPopup(true),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f5f7fa',\n                color: '#2196f3',\n                border: '1px solid #2196f3',\n                '&:hover': {\n                  backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                  borderColor: '#1976d2'\n                }\n              },\n              children: \"Lista Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#2196f3',\n                color: 'white',\n                '&:hover': {\n                  backgroundColor: '#1976d2'\n                }\n              },\n              children: \"Crea Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), loadingComande ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 13\n        }, this) : allComande.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Crea la prima comanda per iniziare a gestire i lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              backgroundColor: '#2196f3',\n              color: 'white',\n              '&:hover': {\n                backgroundColor: '#1976d2'\n              }\n            },\n            children: \"Crea Prima Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ComandeListTable, {\n          comande: allComande,\n          onEditComanda: handleOpenComandaDialog,\n          onDeleteComanda: handleDeleteComanda,\n          onInserimentoMetri: handleOpenInserimentoMetri,\n          onPrintComanda: handlePrintComanda,\n          loading: loadingComande\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 755,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: [\"Modifica Comanda \", selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.codice_comanda]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Stato: \", selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.stato, \" \\u2022 Cavi assegnati: \", (selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.numero_cavi_assegnati) || 0]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 836,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Attenzione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 17\n              }, this), \" Modificare il tipo di comanda riassegner\\xE0 automaticamente tutti i cavi al nuovo tipo. Assicurati che i cavi siano compatibili con il nuovo tipo di operazione.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            select: true,\n            label: \"Tipo Comanda\",\n            value: formDataComanda.tipo_comanda,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              tipo_comanda: e.target.value\n            }),\n            margin: \"normal\",\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"POSA\",\n              children: \"Posa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_PARTENZA\",\n              children: \"Collegamento Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_ARRIVO\",\n              children: \"Collegamento Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"CERTIFICAZIONE\",\n              children: \"Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Descrizione\",\n            value: formDataComanda.descrizione,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              descrizione: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3,\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Responsabile\",\n            value: formDataComanda.responsabile,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Note Capo Cantiere\",\n            value: formDataComanda.note_capo_cantiere,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              note_capo_cantiere: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 2,\n            helperText: \"Istruzioni specifiche per il responsabile\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Data Scadenza\",\n            type: \"date\",\n            value: formDataComanda.data_scadenza,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              data_scadenza: e.target.value\n            }),\n            margin: \"normal\",\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 916,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 922,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 915,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 827,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: (response, successMessage) => {\n        console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n        // Mostra messaggio di successo se fornito\n        if (successMessage) {\n          // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n          console.log('📢 Successo:', successMessage);\n        }\n\n        // Ricarica tutti i dati per aggiornare l'interfaccia\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n        console.log('✅ Interfaccia aggiornata');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 937,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsabiliListPopup, {\n      open: openResponsabiliPopup,\n      onClose: () => setOpenResponsabiliPopup(false),\n      responsabili: responsabili,\n      comandePerResponsabile: comandePerResponsabile,\n      onEditResponsabile: responsabile => {\n        setOpenResponsabiliPopup(false);\n        handleOpenResponsabileDialog('edit', responsabile);\n      },\n      onDeleteResponsabile: async idResponsabile => {\n        await handleDeleteResponsabile(idResponsabile);\n        setOpenResponsabiliPopup(false);\n      },\n      loading: loadingResponsabili,\n      error: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 961,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InserimentoMetriDialog, {\n      open: openInserimentoMetri,\n      onClose: handleCloseInserimentoMetri,\n      comanda: comandaPerMetri,\n      onSuccess: handleSuccessInserimentoMetri\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 979,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CollegamentiDialog, {\n      open: openCollegamenti,\n      onClose: handleCloseCollegamenti,\n      comanda: comandaPerCollegamenti,\n      onSuccess: handleSuccessCollegamenti\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 987,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 547,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"z85HBrPxMvigfS49H7k5EdvaEB0=\", false, function () {\n  return [useSearchParams];\n});\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "People", "PeopleIcon", "Construction", "ConstructionIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "ResponsabiliListPopup", "ComandeListTable", "InserimentoMetriDialog", "CollegamentiDialog", "jsxDEV", "_jsxDEV", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "searchParams", "setSearchParams", "loading", "setLoading", "error", "setError", "searchingComanda", "setSearchingComanda", "statistiche", "setStatistiche", "allComande", "setAllComande", "loadingComande", "setLoadingComande", "openResponsabiliPopup", "setOpenResponsabiliPopup", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "note_capo_cantiere", "openInserimentoMetri", "setOpenInserimentoMetri", "comandaPerMetri", "setComandaPerMetri", "openCollegamenti", "setOpenCollegamenti", "comandaPerCollegamenti", "setComandaPerCollegamenti", "loadComande", "console", "log", "comandeData", "getComande", "Array", "isArray", "comandeArray", "comande", "data", "length", "err", "loadStatistiche", "stats", "getStatisticheComande", "_err$response", "response", "message", "loadResponsabili", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response2", "_err$response2$data", "errorMessage", "detail", "initializeData", "Promise", "all", "comandaParam", "get", "Object", "keys", "comandaTrovata", "comandeResp", "id_responsabile", "find", "c", "codice_comanda", "isWorkflowCommand", "includes", "handleOpenInserimentoMetri", "handleOpenComandaDialog", "setTimeout", "prev", "newParams", "URLSearchParams", "delete", "warn", "for<PERSON>ach", "resp", "cmd", "responsabiliList", "startTime", "performance", "now", "comandeMap", "comanda", "r", "push", "endTime", "toFixed", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "handleCloseInserimentoMetri", "handleCloseCollegamenti", "handleSuccessInserimentoMetri", "handleSuccessCollegamenti", "handlePrintComanda", "formato", "prompt", "formatoNome", "alert", "numero_cavi_assegnati", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "width", "height", "borderRadius", "Math", "round", "comande_create", "gap", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "borderColor", "elevation", "textAlign", "gutterBottom", "onEditComanda", "onDeleteComanda", "onInserimentoMetri", "onPrintComanda", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "pt", "label", "value", "onChange", "e", "target", "margin", "required", "type", "helperText", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "successMessage", "onEditResponsabile", "onDeleteResponsabile", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  List,\n  ListItem,\n  ListItemText,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  People as PeopleIcon,\n  Construction as ConstructionIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\nimport CollegamentiDialog from './CollegamentiDialog';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n\n  // Stati per dialog collegamenti\n  const [openCollegamenti, setOpenCollegamenti] = useState(false);\n  const [comandaPerCollegamenti, setComandaPerCollegamenti] = useState(null);\n\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const comandeData = await comandeService.getComande(cantiereId);\n      console.log('📋 Dati comande ricevuti:', comandeData);\n      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n\n      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', err.response?.data || err.message);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([\n            loadResponsabili(),\n            loadComande(),\n            loadStatistiche()\n          ]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n\n        // Per comande di workflow (POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO) provenienti da visualizza cavi,\n        // apri l'inserimento dati specifico. Per altre comande, apri il dialog di modifica generale\n        const isWorkflowCommand = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comandaTrovata.tipo_comanda);\n\n        if (isWorkflowCommand) {\n          console.log(`📏 Apertura gestione per comanda ${comandaTrovata.tipo_comanda} da visualizza cavi:`, comandaParam);\n          handleOpenInserimentoMetri(comandaTrovata);\n        } else {\n          console.log('✏️ Apertura modifica per comanda non-workflow:', comandaParam);\n          handleOpenComandaDialog(comandaTrovata);\n        }\n\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      console.log('🚀 Caricamento comande per responsabili ottimizzato...');\n      const startTime = performance.now();\n\n      // OTTIMIZZAZIONE: Carica tutte le comande una sola volta invece di fare N chiamate\n      const allComande = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(allComande)) {\n        comandeArray = allComande;\n      } else if (allComande && Array.isArray(allComande.comande)) {\n        comandeArray = allComande.comande;\n      } else if (allComande && Array.isArray(allComande.data)) {\n        comandeArray = allComande.data;\n      }\n\n      // Raggruppa le comande per responsabile\n      const comandeMap = {};\n\n      // Inizializza tutti i responsabili con array vuoto\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n\n      // Raggruppa le comande per responsabile\n      comandeArray.forEach(comanda => {\n        const responsabile = responsabiliList.find(r => r.nome_responsabile === comanda.responsabile);\n        if (responsabile) {\n          comandeMap[responsabile.id_responsabile].push(comanda);\n        }\n      });\n\n      const endTime = performance.now();\n      console.log(`✅ Comande caricate in ${(endTime - startTime).toFixed(2)}ms (1 chiamata invece di ${responsabiliList.length})`);\n\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle comande:', err);\n      // Fallback: inizializza con array vuoti\n      const comandeMap = {};\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n      setComandePerResponsabile(comandeMap);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (comanda) => {\n    setSelectedComanda(comanda);\n\n    if (comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n      handleCloseComandaDialog();\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  // Gestione inserimento metri e collegamenti\n  const handleOpenInserimentoMetri = (comanda) => {\n    if (comanda.tipo_comanda === 'POSA') {\n      // Per POSA: apri dialog inserimento metri\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    } else if (['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda)) {\n      // Per COLLEGAMENTI: apri dialog collegamenti\n      setComandaPerCollegamenti(comanda);\n      setOpenCollegamenti(true);\n    } else {\n      // Per altri tipi: apri dialog inserimento metri (fallback)\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    }\n  };\n\n  const handleCloseInserimentoMetri = () => {\n    setOpenInserimentoMetri(false);\n    setComandaPerMetri(null);\n  };\n\n  const handleCloseCollegamenti = () => {\n    setOpenCollegamenti(false);\n    setComandaPerCollegamenti(null);\n  };\n\n  const handleSuccessInserimentoMetri = async (message) => {\n    console.log('✅ Successo inserimento metri:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([\n      loadResponsabili(),\n      loadComande(),\n      loadStatistiche()\n    ]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  const handleSuccessCollegamenti = async (message) => {\n    console.log('✅ Successo collegamenti:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([\n      loadResponsabili(),\n      loadComande(),\n      loadStatistiche()\n    ]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  // Gestione stampa comanda\n  const handlePrintComanda = (comanda) => {\n    console.log('🖨️ Stampa comanda:', comanda.codice_comanda);\n\n    // Per ora mostra un dialog di selezione formato\n    const formato = window.prompt(\n      `Seleziona il formato di stampa per la comanda ${comanda.codice_comanda}:\\n\\n` +\n      '1 - A4 (Formato standard)\\n' +\n      '2 - A3 (Formato esteso)\\n\\n' +\n      'Inserisci 1 o 2:',\n      '1'\n    );\n\n    if (formato === '1' || formato === '2') {\n      const formatoNome = formato === '1' ? 'A4' : 'A3';\n      console.log(`📄 Generazione comanda ${comanda.codice_comanda} in formato ${formatoNome}`);\n\n      // TODO: Implementare la generazione del PDF\n      alert(`Funzionalità in sviluppo!\\n\\nComanda: ${comanda.codice_comanda}\\nFormato: ${formatoNome}\\nTipo: ${comanda.tipo_comanda}\\nResponsabile: ${comanda.responsabile}\\nCavi: ${comanda.numero_cavi_assegnati || 0}`);\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n\n\n  if (loading || loadingComande) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {searchingComanda && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          🔍 Ricerca comanda {searchingComanda} in corso...\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Comande - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Gestione Responsabili e Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<PeopleIcon />}\n                onClick={() => setOpenResponsabiliPopup(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#f5f7fa',\n                  color: '#2196f3',\n                  border: '1px solid #2196f3',\n                  '&:hover': {\n                    backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                    borderColor: '#1976d2'\n                  }\n                }}\n              >\n                Lista Responsabili\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => handleOpenResponsabileDialog('create')}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Responsabile\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Lista Comande in stile tabella */}\n          {loadingComande ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : allComande.length === 0 ? (\n            <Paper\n              elevation={0}\n              sx={{\n                p: 6,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              }}\n            >\n              <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Nessuna comanda disponibile\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                Crea la prima comanda per iniziare a gestire i lavori\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Prima Comanda\n              </Button>\n            </Paper>\n          ) : (\n            <ComandeListTable\n              comande={allComande}\n              onEditComanda={handleOpenComandaDialog}\n              onDeleteComanda={handleDeleteComanda}\n              onInserimentoMetri={handleOpenInserimentoMetri}\n              onPrintComanda={handlePrintComanda}\n              loading={loadingComande}\n            />\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            Modifica Comanda {selectedComanda?.codice_comanda}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Stato: {selectedComanda?.stato} • Cavi assegnati: {selectedComanda?.numero_cavi_assegnati || 0}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              <Typography variant=\"body2\">\n                <strong>Attenzione:</strong> Modificare il tipo di comanda riassegnerà automaticamente tutti i cavi al nuovo tipo.\n                Assicurati che i cavi siano compatibili con il nuovo tipo di operazione.\n              </Typography>\n            </Alert>\n\n            <TextField\n              fullWidth\n              select\n              label=\"Tipo Comanda\"\n              value={formDataComanda.tipo_comanda}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n              margin=\"normal\"\n              sx={{ mb: 2 }}\n            >\n              <MenuItem value=\"POSA\">Posa</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n              <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n            </TextField>\n\n            <TextField\n              fullWidth\n              label=\"Descrizione\"\n              value={formDataComanda.descrizione}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={3}\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Responsabile\"\n              value={formDataComanda.responsabile}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Note Capo Cantiere\"\n              value={formDataComanda.note_capo_cantiere}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={2}\n              helperText=\"Istruzioni specifiche per il responsabile\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Data Scadenza\"\n              type=\"date\"\n              value={formDataComanda.data_scadenza}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n              margin=\"normal\"\n              InputLabelProps={{\n                shrink: true,\n              }}\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitComanda}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            Salva Modifiche\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={(response, successMessage) => {\n          console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n          // Mostra messaggio di successo se fornito\n          if (successMessage) {\n            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n            console.log('📢 Successo:', successMessage);\n          }\n\n          // Ricarica tutti i dati per aggiornare l'interfaccia\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n\n          console.log('✅ Interfaccia aggiornata');\n        }}\n      />\n\n      {/* Popup Lista Responsabili */}\n      <ResponsabiliListPopup\n        open={openResponsabiliPopup}\n        onClose={() => setOpenResponsabiliPopup(false)}\n        responsabili={responsabili}\n        comandePerResponsabile={comandePerResponsabile}\n        onEditResponsabile={(responsabile) => {\n          setOpenResponsabiliPopup(false);\n          handleOpenResponsabileDialog('edit', responsabile);\n        }}\n        onDeleteResponsabile={async (idResponsabile) => {\n          await handleDeleteResponsabile(idResponsabile);\n          setOpenResponsabiliPopup(false);\n        }}\n        loading={loadingResponsabili}\n        error={error}\n      />\n\n      {/* Dialog Inserimento Metri */}\n      <InserimentoMetriDialog\n        open={openInserimentoMetri}\n        onClose={handleCloseInserimentoMetri}\n        comanda={comandaPerMetri}\n        onSuccess={handleSuccessInserimentoMetri}\n      />\n\n      {/* Dialog Collegamenti */}\n      <CollegamentiDialog\n        open={openCollegamenti}\n        onClose={handleCloseCollegamenti}\n        comanda={comandaPerCollegamenti}\n        onSuccess={handleSuccessCollegamenti}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/C,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC8D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACwE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC0E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3E,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC;IAC/DgF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqF,eAAe,EAAEC,kBAAkB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuF,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAC;IACrDyF,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACkG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EAE1E,MAAMsG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFzC,iBAAiB,CAAC,IAAI,CAAC;MACvB0C,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE3D,UAAU,CAAC;MAC/D,MAAM4D,WAAW,GAAG,MAAMtE,cAAc,CAACuE,UAAU,CAAC7D,UAAU,CAAC;MAC/D0D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,WAAW,CAAC;MACrDF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOC,WAAW,EAAE,QAAQ,EAAEE,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,CAAC;;MAEtF;MACA,IAAII,YAAY,GAAG,EAAE;MACrB,IAAIF,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QAC9BI,YAAY,GAAGJ,WAAW;MAC5B,CAAC,MAAM,IAAIA,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAACK,OAAO,CAAC,EAAE;QAC5DD,YAAY,GAAGJ,WAAW,CAACK,OAAO;MACpC,CAAC,MAAM,IAAIL,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAACM,IAAI,CAAC,EAAE;QACzDF,YAAY,GAAGJ,WAAW,CAACM,IAAI;MACjC;MAEAR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,YAAY,EAAE,YAAY,EAAEA,YAAY,CAACG,MAAM,CAAC;MACxFrD,aAAa,CAACkD,YAAY,CAAC;IAC7B,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZV,OAAO,CAACnD,KAAK,CAAC,iCAAiC,EAAE6D,GAAG,CAAC;MACrD5D,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRQ,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMqD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFX,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE3D,UAAU,CAAC;MACnE,MAAMsE,KAAK,GAAG,MAAMhF,cAAc,CAACiF,qBAAqB,CAACvE,UAAU,CAAC;MACpE0D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEW,KAAK,CAAC;MAC9C1D,cAAc,CAAC0D,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MAAA,IAAAI,aAAA;MACZd,OAAO,CAACnD,KAAK,CAAC,6CAA6C,EAAE6D,GAAG,CAAC;MACjEV,OAAO,CAACnD,KAAK,CAAC,oBAAoB,EAAE,EAAAiE,aAAA,GAAAJ,GAAG,CAACK,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcN,IAAI,KAAIE,GAAG,CAACM,OAAO,CAAC;IACxE;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFnD,sBAAsB,CAAC,IAAI,CAAC;MAC5BhB,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM0D,IAAI,GAAG,MAAM3E,mBAAmB,CAACqF,uBAAuB,CAAC5E,UAAU,CAAC;MAC1EsB,eAAe,CAAC4C,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMW,0BAA0B,CAACX,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOE,GAAG,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACZrB,OAAO,CAACnD,KAAK,CAAC,0CAA0C,EAAE6D,GAAG,CAAC;MAC9D,MAAMY,YAAY,GAAG,EAAAF,cAAA,GAAAV,GAAG,CAACK,QAAQ,cAAAK,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,uBAAlBA,mBAAA,CAAoBE,MAAM,KAAIb,GAAG,CAACM,OAAO,IAAI,yCAAyC;MAC3GlE,QAAQ,CAAC,4CAA4CwE,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACRxD,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAID;EACApE,SAAS,CAAC,MAAM;IACd,MAAM8H,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAIlF,UAAU,EAAE;QACdM,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAM6E,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClBlB,WAAW,CAAC,CAAC,EACbY,eAAe,CAAC,CAAC,CAClB,CAAC;QACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;UACZV,OAAO,CAACnD,KAAK,CAAC,kCAAkC,EAAE6D,GAAG,CAAC;QACxD,CAAC,SAAS;UACR9D,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC;IAED4E,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAClF,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA5C,SAAS,CAAC,MAAM;IACd,MAAMiI,YAAY,GAAGlF,YAAY,CAACmF,GAAG,CAAC,SAAS,CAAC;IAChD5B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0B,YAAY,CAAC;IAChE3B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5BtC,YAAY,EAAEA,YAAY,CAAC8C,MAAM;MACjC1C,sBAAsB,EAAE8D,MAAM,CAACC,IAAI,CAAC/D,sBAAsB,CAAC,CAAC0C,MAAM;MAClE9D,OAAO;MACPkB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI8D,YAAY,IAAIA,YAAY,KAAK5E,gBAAgB,EAAE;MACrDC,mBAAmB,CAAC2E,YAAY,CAAC;IACnC;IAEA,IAAIA,YAAY,IAAIhE,YAAY,CAAC8C,MAAM,GAAG,CAAC,IAAIoB,MAAM,CAACC,IAAI,CAAC/D,sBAAsB,CAAC,CAAC0C,MAAM,GAAG,CAAC,EAAE;MAC7FT,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAI8B,cAAc,GAAG,IAAI;MAEzB,KAAK,MAAM3C,YAAY,IAAIzB,YAAY,EAAE;QACvC,MAAMqE,WAAW,GAAGjE,sBAAsB,CAACqB,YAAY,CAAC6C,eAAe,CAAC,IAAI,EAAE;QAC9EjC,OAAO,CAACC,GAAG,CAAC,mBAAmBb,YAAY,CAACX,iBAAiB,KAAKuD,WAAW,CAACvB,MAAM,UAAU,CAAC;QAC/FsB,cAAc,GAAGC,WAAW,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAKT,YAAY,CAAC;QACzE,IAAII,cAAc,EAAE;UAClB/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8B,cAAc,CAAC;UACjD;QACF;MACF;MAEA,IAAIA,cAAc,EAAE;QAClB/B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE0B,YAAY,CAAC;QACnE3E,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;QAE3B;QACA;QACA,MAAMqF,iBAAiB,GAAG,CAAC,MAAM,EAAE,uBAAuB,EAAE,qBAAqB,CAAC,CAACC,QAAQ,CAACP,cAAc,CAAC7C,YAAY,CAAC;QAExH,IAAImD,iBAAiB,EAAE;UACrBrC,OAAO,CAACC,GAAG,CAAC,oCAAoC8B,cAAc,CAAC7C,YAAY,sBAAsB,EAAEyC,YAAY,CAAC;UAChHY,0BAA0B,CAACR,cAAc,CAAC;QAC5C,CAAC,MAAM;UACL/B,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE0B,YAAY,CAAC;UAC3Ea,uBAAuB,CAACT,cAAc,CAAC;QACzC;;QAEA;QACAU,UAAU,CAAC,MAAM;UACf/F,eAAe,CAACgG,IAAI,IAAI;YACtB,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACF,IAAI,CAAC;YAC3CC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;YAC3B,OAAOF,SAAS;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL3C,OAAO,CAAC8C,IAAI,CAAC,yBAAyB,EAAEnB,YAAY,CAAC;QACrD3B,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtCtC,YAAY,CAACoF,OAAO,CAACC,IAAI,IAAI;UAC3B,MAAMzC,OAAO,GAAGxC,sBAAsB,CAACiF,IAAI,CAACf,eAAe,CAAC,IAAI,EAAE;UAClE1B,OAAO,CAACwC,OAAO,CAACE,GAAG,IAAI;YACrBjD,OAAO,CAACC,GAAG,CAAC,OAAOgD,GAAG,CAACb,cAAc,KAAKY,IAAI,CAACvE,iBAAiB,GAAG,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAAC9B,OAAO,IAAI,CAACkB,mBAAmB,EAAE;UACpCmC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpDwC,UAAU,CAAC,MAAM;YACfxB,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM,IAAIU,YAAY,EAAE;MACvB3B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAACtD,OAAO,IAAI,CAACkB,mBAAmB,IAAIF,YAAY,CAAC8C,MAAM,KAAK,CAAC,EAAE;QACjET,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDgB,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACxE,YAAY,EAAEkB,YAAY,EAAEI,sBAAsB,EAAEpB,OAAO,EAAEkB,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExF,MAAMsD,0BAA0B,GAAG,MAAO+B,gBAAgB,IAAK;IAC7D,IAAI;MACFlD,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMkD,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;;MAEnC;MACA,MAAMlG,UAAU,GAAG,MAAMvB,cAAc,CAACuE,UAAU,CAAC7D,UAAU,CAAC;;MAE9D;MACA,IAAIgE,YAAY,GAAG,EAAE;MACrB,IAAIF,KAAK,CAACC,OAAO,CAAClD,UAAU,CAAC,EAAE;QAC7BmD,YAAY,GAAGnD,UAAU;MAC3B,CAAC,MAAM,IAAIA,UAAU,IAAIiD,KAAK,CAACC,OAAO,CAAClD,UAAU,CAACoD,OAAO,CAAC,EAAE;QAC1DD,YAAY,GAAGnD,UAAU,CAACoD,OAAO;MACnC,CAAC,MAAM,IAAIpD,UAAU,IAAIiD,KAAK,CAACC,OAAO,CAAClD,UAAU,CAACqD,IAAI,CAAC,EAAE;QACvDF,YAAY,GAAGnD,UAAU,CAACqD,IAAI;MAChC;;MAEA;MACA,MAAM8C,UAAU,GAAG,CAAC,CAAC;;MAErB;MACAJ,gBAAgB,CAACH,OAAO,CAAC3D,YAAY,IAAI;QACvCkE,UAAU,CAAClE,YAAY,CAAC6C,eAAe,CAAC,GAAG,EAAE;MAC/C,CAAC,CAAC;;MAEF;MACA3B,YAAY,CAACyC,OAAO,CAACQ,OAAO,IAAI;QAC9B,MAAMnE,YAAY,GAAG8D,gBAAgB,CAAChB,IAAI,CAACsB,CAAC,IAAIA,CAAC,CAAC/E,iBAAiB,KAAK8E,OAAO,CAACnE,YAAY,CAAC;QAC7F,IAAIA,YAAY,EAAE;UAChBkE,UAAU,CAAClE,YAAY,CAAC6C,eAAe,CAAC,CAACwB,IAAI,CAACF,OAAO,CAAC;QACxD;MACF,CAAC,CAAC;MAEF,MAAMG,OAAO,GAAGN,WAAW,CAACC,GAAG,CAAC,CAAC;MACjCrD,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAACyD,OAAO,GAAGP,SAAS,EAAEQ,OAAO,CAAC,CAAC,CAAC,4BAA4BT,gBAAgB,CAACzC,MAAM,GAAG,CAAC;MAE5HzC,yBAAyB,CAACsF,UAAU,CAAC;IACvC,CAAC,CAAC,OAAO5C,GAAG,EAAE;MACZV,OAAO,CAACnD,KAAK,CAAC,yCAAyC,EAAE6D,GAAG,CAAC;MAC7D;MACA,MAAM4C,UAAU,GAAG,CAAC,CAAC;MACrBJ,gBAAgB,CAACH,OAAO,CAAC3D,YAAY,IAAI;QACvCkE,UAAU,CAAClE,YAAY,CAAC6C,eAAe,CAAC,GAAG,EAAE;MAC/C,CAAC,CAAC;MACFjE,yBAAyB,CAACsF,UAAU,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAMM,4BAA4B,GAAGA,CAACC,IAAI,EAAEzE,YAAY,GAAG,IAAI,KAAK;IAClEhB,yBAAyB,CAACyF,IAAI,CAAC;IAC/BvF,uBAAuB,CAACc,YAAY,CAAC;IAErC,IAAIyE,IAAI,KAAK,MAAM,IAAIzE,YAAY,EAAE;MACnCZ,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEW,YAAY,CAACX,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEU,YAAY,CAACV,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAES,YAAY,CAACT,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAM4F,6BAA6B,GAAGA,CAAA,KAAM;IAC1C5F,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BxB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMiH,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFjH,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACyB,oBAAoB,CAACE,iBAAiB,CAACuF,IAAI,CAAC,CAAC,EAAE;QAClDlH,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACyB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjE7B,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIqB,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAMtC,mBAAmB,CAACoI,kBAAkB,CAAC3H,UAAU,EAAEiC,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAMtC,mBAAmB,CAACqI,kBAAkB,CAAC7F,oBAAoB,CAAC4D,eAAe,EAAE1D,oBAAoB,CAAC;MAC1G;MAEAuF,6BAA6B,CAAC,CAAC;MAC/B,MAAM7C,gBAAgB,CAAC,CAAC;MACxB,MAAMlB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZV,OAAO,CAACnD,KAAK,CAAC,yBAAyB,EAAE6D,GAAG,CAAC;MAC7C5D,QAAQ,CAAC4D,GAAG,CAACa,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAM4C,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMzI,mBAAmB,CAAC0I,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAMnD,gBAAgB,CAAC,CAAC;MACxB,MAAMlB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZV,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAE6D,GAAG,CAAC;MAChD5D,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAM0F,uBAAuB,GAAIe,OAAO,IAAK;IAC3CxE,kBAAkB,CAACwE,OAAO,CAAC;IAE3B,IAAIA,OAAO,EAAE;MACXtE,kBAAkB,CAAC;QACjBC,YAAY,EAAEqE,OAAO,CAACrE,YAAY;QAClCC,WAAW,EAAEoE,OAAO,CAACpE,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEmE,OAAO,CAACnE,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEkE,OAAO,CAAClE,aAAa,IAAI,EAAE;QAC1CC,kBAAkB,EAAEiE,OAAO,CAACjE,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAT,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM2F,wBAAwB,GAAGA,CAAA,KAAM;IACrC3F,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmF,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAM7I,cAAc,CAAC8I,aAAa,CAAC5F,eAAe,CAACsD,cAAc,EAAEpD,eAAe,CAAC;MACnFwF,wBAAwB,CAAC,CAAC;MAC1B,MAAMvD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAMlB,WAAW,CAAC,CAAC;MACnB,MAAMY,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZV,OAAO,CAACnD,KAAK,CAAC,yBAAyB,EAAE6D,GAAG,CAAC;MAC7C5D,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAM6H,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACP,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAM1I,cAAc,CAACiJ,aAAa,CAACD,aAAa,CAAC;MACjD,MAAM3D,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAMlB,WAAW,CAAC,CAAC;MACnB,MAAMY,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZV,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAE6D,GAAG,CAAC;MAChD5D,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMyF,0BAA0B,GAAIgB,OAAO,IAAK;IAC9C,IAAIA,OAAO,CAACrE,YAAY,KAAK,MAAM,EAAE;MACnC;MACAQ,kBAAkB,CAAC6D,OAAO,CAAC;MAC3B/D,uBAAuB,CAAC,IAAI,CAAC;IAC/B,CAAC,MAAM,IAAI,CAAC,uBAAuB,EAAE,qBAAqB,CAAC,CAAC8C,QAAQ,CAACiB,OAAO,CAACrE,YAAY,CAAC,EAAE;MAC1F;MACAY,yBAAyB,CAACyD,OAAO,CAAC;MAClC3D,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACL;MACAF,kBAAkB,CAAC6D,OAAO,CAAC;MAC3B/D,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;EAED,MAAMsF,2BAA2B,GAAGA,CAAA,KAAM;IACxCtF,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMqF,uBAAuB,GAAGA,CAAA,KAAM;IACpCnF,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMkF,6BAA6B,GAAG,MAAOhE,OAAO,IAAK;IACvDhB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEe,OAAO,CAAC;IACrDlE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAM2E,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClBlB,WAAW,CAAC,CAAC,EACbY,eAAe,CAAC,CAAC,CAClB,CAAC;;IAEF;IACA;EACF,CAAC;EAED,MAAMsE,yBAAyB,GAAG,MAAOjE,OAAO,IAAK;IACnDhB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEe,OAAO,CAAC;IAChDlE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAM2E,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClBlB,WAAW,CAAC,CAAC,EACbY,eAAe,CAAC,CAAC,CAClB,CAAC;;IAEF;IACA;EACF,CAAC;;EAED;EACA,MAAMuE,kBAAkB,GAAI3B,OAAO,IAAK;IACtCvD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEsD,OAAO,CAACnB,cAAc,CAAC;;IAE1D;IACA,MAAM+C,OAAO,GAAGd,MAAM,CAACe,MAAM,CAC3B,iDAAiD7B,OAAO,CAACnB,cAAc,OAAO,GAC9E,6BAA6B,GAC7B,6BAA6B,GAC7B,kBAAkB,EAClB,GACF,CAAC;IAED,IAAI+C,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtC,MAAME,WAAW,GAAGF,OAAO,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI;MACjDnF,OAAO,CAACC,GAAG,CAAC,0BAA0BsD,OAAO,CAACnB,cAAc,eAAeiD,WAAW,EAAE,CAAC;;MAEzF;MACAC,KAAK,CAAC,yCAAyC/B,OAAO,CAACnB,cAAc,cAAciD,WAAW,WAAW9B,OAAO,CAACrE,YAAY,mBAAmBqE,OAAO,CAACnE,YAAY,WAAWmE,OAAO,CAACgC,qBAAqB,IAAI,CAAC,EAAE,CAAC;IACtN;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE;IACpB,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAID,IAAIjJ,OAAO,IAAIU,cAAc,EAAE;IAC7B,oBACEjB,OAAA,CAACxC,GAAG;MAACkM,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E9J,OAAA,CAAC7B,gBAAgB;QAAA4L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACElK,OAAA,CAACxC,GAAG;IAAC2M,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhB9J,OAAA,CAACxC,GAAG;MAAC6M,EAAE,EAAE,CAAE;MAAAP,QAAA,eACT9J,OAAA,CAACvC,UAAU;QAAC6M,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrE3J;MAAY;QAAA4J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELzJ,KAAK,iBACJT,OAAA,CAAC9B,KAAK;MAACuM,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnCrJ;IAAK;MAAAsJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAvJ,gBAAgB,iBACfX,OAAA,CAAC9B,KAAK;MAACuM,QAAQ,EAAC,MAAM;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,GAAC,+BACjB,EAACnJ,gBAAgB,EAAC,cACvC;IAAA;MAAAoJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGArJ,WAAW,iBACVb,OAAA,CAACrC,KAAK;MAACwM,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7C9J,OAAA,CAACzB,KAAK;QAACoM,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnG9J,OAAA,CAACzB,KAAK;UAACoM,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD9J,OAAA,CAACjB,UAAU;YAACyL,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ClK,OAAA,CAACxC,GAAG;YAAAsM,QAAA,gBACF9J,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DjJ,WAAW,CAACmK,mBAAmB,IAAI;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACblK,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRlK,OAAA,CAACzB,KAAK;UAACoM,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD9J,OAAA,CAACnB,UAAU;YAAC2L,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5ClK,OAAA,CAACxC,GAAG;YAAAsM,QAAA,gBACF9J,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DjJ,WAAW,CAACoK,cAAc,IAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACblK,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRlK,OAAA,CAACzB,KAAK;UAACoM,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD9J,OAAA,CAACf,eAAe;YAACuL,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDlK,OAAA,CAACxC,GAAG;YAAAsM,QAAA,gBACF9J,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DjJ,WAAW,CAACqK,gBAAgB,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACblK,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRlK,OAAA,CAACzB,KAAK;UAACoM,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD9J,OAAA,CAACb,YAAY;YAACqL,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDlK,OAAA,CAACxC,GAAG;YAAAsM,QAAA,gBACF9J,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DjJ,WAAW,CAACsK,kBAAkB,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACblK,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRlK,OAAA,CAACzB,KAAK;UAACoM,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD9J,OAAA,CAACxC,GAAG;YAAC2M,EAAE,EAAE;cACPiB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAG7J,WAAW,CAACsK,kBAAkB,IAAItK,WAAW,CAACoK,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3FpK,WAAW,CAACsK,kBAAkB,IAAItK,WAAW,CAACoK,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHvB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACA9J,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1DjJ,WAAW,CAACoK,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAE3K,WAAW,CAACsK,kBAAkB,GAAGtK,WAAW,CAACoK,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNlK,OAAA,CAACxC,GAAG;YAAAsM,QAAA,gBACF9J,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblK,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjDjJ,WAAW,CAAC4K,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDlK,OAAA,CAACxC,GAAG;MAAAsM,QAAA,eACF9J,OAAA,CAACxC,GAAG;QAAAsM,QAAA,gBAEF9J,OAAA,CAACxC,GAAG;UAACkM,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3E9J,OAAA,CAACvC,UAAU;YAAC6M,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblK,OAAA,CAACxC,GAAG;YAACkM,OAAO,EAAC,MAAM;YAACgC,GAAG,EAAE,CAAE;YAAA5B,QAAA,gBACzB9J,OAAA,CAACtC,MAAM;cACL4M,OAAO,EAAC,UAAU;cAClBqB,SAAS,eAAE3L,OAAA,CAACX,UAAU;gBAAA0K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1B0B,OAAO,EAAEA,CAAA,KAAMxK,wBAAwB,CAAC,IAAI,CAAE;cAC9C+I,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,SAAS;gBAChByB,MAAM,EAAE,mBAAmB;gBAC3B,SAAS,EAAE;kBACTD,eAAe,EAAE,yBAAyB;kBAC1CE,WAAW,EAAE;gBACf;cACF,CAAE;cAAApC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlK,OAAA,CAACtC,MAAM;cACL4M,OAAO,EAAC,WAAW;cACnBqB,SAAS,eAAE3L,OAAA,CAACrB,OAAO;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvB0B,OAAO,EAAEA,CAAA,KAAMpE,4BAA4B,CAAC,QAAQ,CAAE;cACtD2C,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTwB,eAAe,EAAE;gBACnB;cACF,CAAE;cAAAlC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLjJ,cAAc,gBACbjB,OAAA,CAACxC,GAAG;UAACkM,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACoC,EAAE,EAAE,CAAE;UAAAjC,QAAA,eAChD9J,OAAA,CAAC7B,gBAAgB;YAAA4L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJnJ,UAAU,CAACsD,MAAM,KAAK,CAAC,gBACzBrE,OAAA,CAACrC,KAAK;UACJwO,SAAS,EAAE,CAAE;UACbhC,EAAE,EAAE;YACFC,CAAC,EAAE,CAAC;YACJgC,SAAS,EAAE,QAAQ;YACnBJ,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAE;UACf,CAAE;UAAApC,QAAA,gBAEF9J,OAAA,CAACnB,UAAU;YAACsL,EAAE,EAAE;cAAEW,QAAQ,EAAE,EAAE;cAAEN,KAAK,EAAE,UAAU;cAAEH,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DlK,OAAA,CAACvC,UAAU;YAAC6M,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,gBAAgB;YAAC6B,YAAY;YAAAvC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblK,OAAA,CAACvC,UAAU;YAAC6M,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAACL,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblK,OAAA,CAACtC,MAAM;YACL4M,OAAO,EAAC,WAAW;YACnBqB,SAAS,eAAE3L,OAAA,CAACrB,OAAO;cAAAoL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0B,OAAO,EAAEA,CAAA,KAAMtK,kBAAkB,CAAC,IAAI,CAAE;YACxC6I,EAAE,EAAE;cACF0B,aAAa,EAAE,MAAM;cACrBG,eAAe,EAAE,SAAS;cAC1BxB,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACTwB,eAAe,EAAE;cACnB;YACF,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAERlK,OAAA,CAACJ,gBAAgB;UACfuE,OAAO,EAAEpD,UAAW;UACpBuL,aAAa,EAAElG,uBAAwB;UACvCmG,eAAe,EAAEhE,mBAAoB;UACrCiE,kBAAkB,EAAErG,0BAA2B;UAC/CsG,cAAc,EAAE3D,kBAAmB;UACnCvI,OAAO,EAAEU;QAAe;UAAA8I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlK,OAAA,CAACnC,MAAM;MACL6O,IAAI,EAAE7K,sBAAuB;MAC7B8K,OAAO,EAAEjF,6BAA8B;MACvCkF,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF9J,OAAA,CAAClC,WAAW;QAACqM,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzB9J,OAAA,CAACvC,UAAU;UAAC6M,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9C/H,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdlK,OAAA,CAACjC,aAAa;QAAA+L,QAAA,eACZ9J,OAAA,CAACxC,GAAG;UAAC2M,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjB9J,OAAA,CAAC/B,SAAS;YACR4O,SAAS;YACTI,KAAK,EAAC,mBAAmB;YACzBC,KAAK,EAAE/K,oBAAoB,CAACE,iBAAkB;YAC9C8K,QAAQ,EAAGC,CAAC,IAAKhL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAE+K,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzGI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRjD,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFlK,OAAA,CAAC/B,SAAS;YACR4O,SAAS;YACTI,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,OAAO;YACZN,KAAK,EAAE/K,oBAAoB,CAACG,KAAM;YAClC6K,QAAQ,EAAGC,CAAC,IAAKhL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAE8K,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC7FI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC,uDAAuD;YAClEtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFlK,OAAA,CAAC/B,SAAS;YACR4O,SAAS;YACTI,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE/K,oBAAoB,CAACI,QAAS;YACrC4K,QAAQ,EAAGC,CAAC,IAAKhL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAE6K,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC;UAA+C;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBlK,OAAA,CAAChC,aAAa;QAACmM,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjC9J,OAAA,CAACtC,MAAM;UACLkO,OAAO,EAAElE,6BAA8B;UACvCyC,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlK,OAAA,CAACtC,MAAM;UACLkO,OAAO,EAAEjE,wBAAyB;UAClC2C,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EAED/H,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlK,OAAA,CAACnC,MAAM;MACL6O,IAAI,EAAElK,iBAAkB;MACxBmK,OAAO,EAAEvE,wBAAyB;MAClCwE,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF9J,OAAA,CAAClC,WAAW;QAACqM,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,gBACzB9J,OAAA,CAACvC,UAAU;UAAC6M,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,GAAC,mBAC/B,EAACpH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsD,cAAc;QAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACblK,OAAA,CAACvC,UAAU;UAAC6M,OAAO,EAAC,OAAO;UAACE,KAAK,EAAC,gBAAgB;UAAAV,QAAA,GAAC,SAC1C,EAACpH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8G,KAAK,EAAC,0BAAmB,EAAC,CAAA9G,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyG,qBAAqB,KAAI,CAAC;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdlK,OAAA,CAACjC,aAAa;QAAA+L,QAAA,eACZ9J,OAAA,CAACxC,GAAG;UAAC2M,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjB9J,OAAA,CAAC9B,KAAK;YAACuM,QAAQ,EAAC,SAAS;YAACN,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eACtC9J,OAAA,CAACvC,UAAU;cAAC6M,OAAO,EAAC,OAAO;cAAAR,QAAA,gBACzB9J,OAAA;gBAAA8J,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,sKAE9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAERlK,OAAA,CAAC/B,SAAS;YACR4O,SAAS;YACTa,MAAM;YACNT,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAEtK,eAAe,CAACE,YAAa;YACpCqK,QAAQ,EAAGC,CAAC,IAAKvK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEE,YAAY,EAAEsK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC1FI,MAAM,EAAC,QAAQ;YACfnD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBAEd9J,OAAA,CAACxB,QAAQ;cAAC0O,KAAK,EAAC,MAAM;cAAApD,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtClK,OAAA,CAACxB,QAAQ;cAAC0O,KAAK,EAAC,uBAAuB;cAAApD,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxElK,OAAA,CAACxB,QAAQ;cAAC0O,KAAK,EAAC,qBAAqB;cAAApD,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpElK,OAAA,CAACxB,QAAQ;cAAC0O,KAAK,EAAC,gBAAgB;cAAApD,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAEZlK,OAAA,CAAC/B,SAAS;YACR4O,SAAS;YACTI,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAEtK,eAAe,CAACG,WAAY;YACnCoK,QAAQ,EAAGC,CAAC,IAAKvK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEG,WAAW,EAAEqK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzFI,MAAM,EAAC,QAAQ;YACfK,SAAS;YACTC,IAAI,EAAE,CAAE;YACRzD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFlK,OAAA,CAAC/B,SAAS;YACR4O,SAAS;YACTI,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAEtK,eAAe,CAACI,YAAa;YACpCmK,QAAQ,EAAGC,CAAC,IAAKvK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEI,YAAY,EAAEoK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC1FI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRE,UAAU,EAAC,0CAAuC;YAClDtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFlK,OAAA,CAAC/B,SAAS;YACR4O,SAAS;YACTI,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAEtK,eAAe,CAACM,kBAAmB;YAC1CiK,QAAQ,EAAGC,CAAC,IAAKvK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEM,kBAAkB,EAAEkK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfK,SAAS;YACTC,IAAI,EAAE,CAAE;YACRH,UAAU,EAAC,2CAA2C;YACtDtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFlK,OAAA,CAAC/B,SAAS;YACR4O,SAAS;YACTI,KAAK,EAAC,eAAe;YACrBO,IAAI,EAAC,MAAM;YACXN,KAAK,EAAEtK,eAAe,CAACK,aAAc;YACrCkK,QAAQ,EAAGC,CAAC,IAAKvK,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEK,aAAa,EAAEmK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC3FI,MAAM,EAAC,QAAQ;YACfO,eAAe,EAAE;cACfC,MAAM,EAAE;YACV;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBlK,OAAA,CAAChC,aAAa;QAACmM,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjC9J,OAAA,CAACtC,MAAM;UACLkO,OAAO,EAAExD,wBAAyB;UAClC+B,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlK,OAAA,CAACtC,MAAM;UACLkO,OAAO,EAAEvD,mBAAoB;UAC7BiC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlK,OAAA,CAACN,kBAAkB;MACjBQ,UAAU,EAAEA,UAAW;MACvBwM,IAAI,EAAErL,eAAgB;MACtBsL,OAAO,EAAEA,CAAA,KAAMrL,kBAAkB,CAAC,KAAK,CAAE;MACzCyM,SAAS,EAAEA,CAACpJ,QAAQ,EAAEqJ,cAAc,KAAK;QACvCpK,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAImK,cAAc,EAAE;UAClB;UACApK,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEmK,cAAc,CAAC;QAC7C;;QAEA;QACArK,WAAW,CAAC,CAAC;QACbY,eAAe,CAAC,CAAC;QACjBM,gBAAgB,CAAC,CAAC;QAClBvD,kBAAkB,CAAC,KAAK,CAAC;QAEzBsC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC;IAAE;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFlK,OAAA,CAACL,qBAAqB;MACpB+M,IAAI,EAAEvL,qBAAsB;MAC5BwL,OAAO,EAAEA,CAAA,KAAMvL,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,YAAY,EAAEA,YAAa;MAC3BI,sBAAsB,EAAEA,sBAAuB;MAC/CsM,kBAAkB,EAAGjL,YAAY,IAAK;QACpC5B,wBAAwB,CAAC,KAAK,CAAC;QAC/BoG,4BAA4B,CAAC,MAAM,EAAExE,YAAY,CAAC;MACpD,CAAE;MACFkL,oBAAoB,EAAE,MAAOlG,cAAc,IAAK;QAC9C,MAAMD,wBAAwB,CAACC,cAAc,CAAC;QAC9C5G,wBAAwB,CAAC,KAAK,CAAC;MACjC,CAAE;MACFb,OAAO,EAAEkB,mBAAoB;MAC7BhB,KAAK,EAAEA;IAAM;MAAAsJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGFlK,OAAA,CAACH,sBAAsB;MACrB6M,IAAI,EAAEvJ,oBAAqB;MAC3BwJ,OAAO,EAAEjE,2BAA4B;MACrCvB,OAAO,EAAE9D,eAAgB;MACzB0K,SAAS,EAAEnF;IAA8B;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFlK,OAAA,CAACF,kBAAkB;MACjB4M,IAAI,EAAEnJ,gBAAiB;MACvBoJ,OAAO,EAAEhE,uBAAwB;MACjCxB,OAAO,EAAE1D,sBAAuB;MAChCsK,SAAS,EAAElF;IAA0B;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9J,EAAA,CA37BIH,wBAAwB;EAAA,QAEY1C,eAAe;AAAA;AAAA4Q,EAAA,GAFnDlO,wBAAwB;AA67B9B,eAAeA,wBAAwB;AAAC,IAAAkO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}