<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Co<PERSON>a <PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
            color: #1976d2;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group.full-width {
            flex: 1 1 100%;
        }
        
        .form-group.two-thirds {
            flex: 2;
        }
        
        .form-group.one-third {
            flex: 1;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }
        
        .helper-text {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        
        .riepilogo {
            background: #f5f7fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .riepilogo h3 {
            margin: 0 0 15px 0;
            color: #1976d2;
            font-size: 16px;
        }
        
        .riepilogo-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .riepilogo-item {
            font-size: 14px;
        }
        
        .stima-lavoro {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            border-left: 4px solid #1976d2;
        }
        
        .stima-lavoro h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
            font-size: 14px;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #1976d2;
            color: white;
        }
        
        .btn-secondary {
            background: #f5f5f5;
            color: #333;
        }
        
        .btn-primary:hover {
            background: #1565c0;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        @media (max-width: 600px) {
            .form-row {
                flex-direction: column;
                gap: 10px;
            }
            
            .riepilogo-grid {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="form-header">
            <h2>📋 Crea Nuova Comanda con Cavi</h2>
            <p>Form ottimizzato - Compatto e funzionale</p>
        </div>
        
        <form id="comandaForm">
            <!-- Prima riga: Responsabile e Data Scadenza -->
            <div class="form-row">
                <div class="form-group two-thirds">
                    <label for="responsabile">Responsabile *</label>
                    <input type="text" id="responsabile" name="responsabile" required>
                    <div class="helper-text">Chi eseguirà il lavoro (obbligatorio)</div>
                </div>
                
                <div class="form-group one-third">
                    <label for="data_scadenza">Data Scadenza</label>
                    <input type="date" id="data_scadenza" name="data_scadenza">
                    <div class="helper-text">Scadenza prevista</div>
                </div>
            </div>
            
            <!-- Seconda riga: Numero Componenti Squadra -->
            <div class="form-row">
                <div class="form-group one-third">
                    <label for="numero_componenti_squadra">Numero Componenti Squadra</label>
                    <input type="number" id="numero_componenti_squadra" name="numero_componenti_squadra" 
                           value="1" min="1" max="20">
                    <div class="helper-text">Persone previste per il lavoro (per calcolo resa oraria)</div>
                </div>
            </div>
            
            <!-- Terza riga: Note per il Responsabile -->
            <div class="form-row">
                <div class="form-group full-width">
                    <label for="note_capo_cantiere">Note per il Responsabile</label>
                    <textarea id="note_capo_cantiere" name="note_capo_cantiere" rows="3"
                              placeholder="Inserisci eventuali istruzioni specifiche, precauzioni o dettagli tecnici per l'esecuzione del lavoro..."></textarea>
                    <div class="helper-text">Istruzioni specifiche per il responsabile</div>
                </div>
            </div>
            
            <!-- Riepilogo Comanda -->
            <div class="riepilogo" id="riepilogo" style="display: none;">
                <h3>📋 Riepilogo Comanda:</h3>
                <div class="riepilogo-grid" id="riepilogoContent">
                    <!-- Contenuto dinamico -->
                </div>
                
                <!-- Stima Lavoro -->
                <div class="stima-lavoro" id="stimaLavoro" style="display: none;">
                    <h4>💡 Stima Lavoro:</h4>
                    <div id="stimaContent">
                        <!-- Contenuto dinamico -->
                    </div>
                </div>
            </div>
        </form>
        
        <div class="buttons">
            <button type="button" class="btn-secondary">Annulla</button>
            <button type="button" class="btn-primary" id="creaComanda">Crea Comanda POSA</button>
        </div>
    </div>

    <script>
        // Simulazione dati
        const tipoComanda = 'POSA';
        const caviSelezionati = [
            { id_cavo: 'C049', metri_teorici: 150 },
            { id_cavo: 'C050', metri_teorici: 200 },
            { id_cavo: 'C051', metri_teorici: 120 }
        ];
        
        function updateRiepilogo() {
            const responsabile = document.getElementById('responsabile').value;
            const dataScadenza = document.getElementById('data_scadenza').value;
            const numeroComponenti = parseInt(document.getElementById('numero_componenti_squadra').value) || 1;
            
            const riepilogo = document.getElementById('riepilogo');
            const riepilogoContent = document.getElementById('riepilogoContent');
            
            if (responsabile) {
                riepilogo.style.display = 'block';
                
                let content = `
                    <div class="riepilogo-item"><strong>Tipo:</strong> ${tipoComanda}</div>
                    <div class="riepilogo-item"><strong>Cavi selezionati:</strong> ${caviSelezionati.length}</div>
                    <div class="riepilogo-item"><strong>Responsabile:</strong> ${responsabile}</div>
                `;
                
                if (numeroComponenti > 1) {
                    content += `<div class="riepilogo-item"><strong>Squadra:</strong> ${numeroComponenti} persone</div>`;
                }
                
                if (dataScadenza) {
                    const data = new Date(dataScadenza).toLocaleDateString('it-IT');
                    content += `<div class="riepilogo-item"><strong>Scadenza:</strong> ${data}</div>`;
                }
                
                riepilogoContent.innerHTML = content;
                
                // Calcola stima lavoro
                if (dataScadenza && numeroComponenti) {
                    updateStimaLavoro(dataScadenza, numeroComponenti);
                } else {
                    document.getElementById('stimaLavoro').style.display = 'none';
                }
            } else {
                riepilogo.style.display = 'none';
            }
        }
        
        function updateStimaLavoro(dataScadenza, numeroComponenti) {
            const oggi = new Date();
            const scadenza = new Date(dataScadenza);
            const giorniDisponibili = Math.max(1, Math.ceil((scadenza - oggi) / (1000 * 60 * 60 * 24)));
            const oreGiornaliere = 8;
            const oreTotaliDisponibili = giorniDisponibili * oreGiornaliere * numeroComponenti;
            
            const metriTotali = caviSelezionati.reduce((sum, cavo) => sum + (cavo.metri_teorici || 0), 0);
            const resaStimata = metriTotali > 0 ? (oreTotaliDisponibili / metriTotali * numeroComponenti).toFixed(1) : 0;
            
            const stimaLavoro = document.getElementById('stimaLavoro');
            const stimaContent = document.getElementById('stimaContent');
            
            stimaLavoro.style.display = 'block';
            stimaContent.innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                    <span><strong>Giorni disponibili:</strong> ${giorniDisponibili}</span>
                    <span><strong>Ore totali squadra:</strong> ${oreTotaliDisponibili}h</span>
                    <span><strong>Metri totali:</strong> ${metriTotali}m</span>
                    <span><strong>Resa richiesta:</strong> ${resaStimata} m/h per persona</span>
                </div>
            `;
        }
        
        // Event listeners
        document.getElementById('responsabile').addEventListener('input', updateRiepilogo);
        document.getElementById('data_scadenza').addEventListener('change', updateRiepilogo);
        document.getElementById('numero_componenti_squadra').addEventListener('input', updateRiepilogo);
        
        document.getElementById('creaComanda').addEventListener('click', function() {
            alert('Form ottimizzato! Tutti i campi sono compatti e funzionali.');
        });
        
        // Inizializza con valori di esempio
        document.getElementById('responsabile').value = 'Mario Rossi';
        document.getElementById('data_scadenza').value = '2024-02-15';
        document.getElementById('numero_componenti_squadra').value = '3';
        updateRiepilogo();
    </script>
</body>
</html>
